<?xml version="1.0" encoding="UTF-8"?>
<protocol name="idle_inhibit_unstable_v1">

  <copyright>
    Copyright © 2015 Samsung Electronics Co., Ltd

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <interface name="zwp_idle_inhibit_manager_v1" version="1">
    <description summary="control behavior when display idles">
      This interface permits inhibiting the idle behavior such as screen
      blanking, locking, and screensaving.  The client binds the idle manager
      globally, then creates idle-inhibitor objects for each surface.

      Warning! The protocol described in this file is experimental and
      backward incompatible changes may be made. Backward compatible changes
      may be added together with the corresponding interface version bump.
      Backward incompatible changes are done by bumping the version number in
      the protocol and interface names and resetting the interface version.
      Once the protocol is to be declared stable, the 'z' prefix and the
      version number in the protocol and interface names are removed and the
      interface version number is reset.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the idle inhibitor object">
	Destroy the inhibit manager.
      </description>
    </request>

    <request name="create_inhibitor">
      <description summary="create a new inhibitor object">
	Create a new inhibitor object associated with the given surface.
      </description>
      <arg name="id" type="new_id" interface="zwp_idle_inhibitor_v1"/>
      <arg name="surface" type="object" interface="wl_surface"
	   summary="the surface that inhibits the idle behavior"/>
    </request>

  </interface>

  <interface name="zwp_idle_inhibitor_v1" version="1">
    <description summary="context object for inhibiting idle behavior">
      An idle inhibitor prevents the output that the associated surface is
      visible on from being set to a state where it is not visually usable due
      to lack of user interaction (e.g. blanked, dimmed, locked, set to power
      save, etc.)  Any screensaver processes are also blocked from displaying.

      If the surface is destroyed, unmapped, becomes occluded, loses
      visibility, or otherwise becomes not visually relevant for the user, the
      idle inhibitor will not be honored by the compositor; if the surface
      subsequently regains visibility the inhibitor takes effect once again.
      Likewise, the inhibitor isn't honored if the system was already idled at
      the time the inhibitor was established, although if the system later
      de-idles and re-idles the inhibitor will take effect.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the idle inhibitor object">
	Remove the inhibitor effect from the associated wl_surface.
      </description>
    </request>

  </interface>
</protocol>
