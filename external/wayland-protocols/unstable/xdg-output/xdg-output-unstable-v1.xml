<?xml version="1.0" encoding="UTF-8"?>
<protocol name="xdg_output_unstable_v1">

  <copyright>
    Copyright © 2017 Red Hat Inc.

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <description summary="Protocol to describe output regions">
    This protocol aims at describing outputs in a way which is more in line
    with the concept of an output on desktop oriented systems.

    Some information are more specific to the concept of an output for
    a desktop oriented system and may not make sense in other applications,
    such as IVI systems for example.

    Typically, the global compositor space on a desktop system is made of
    a contiguous or overlapping set of rectangular regions.

    The logical_position and logical_size events defined in this protocol
    might provide information identical to their counterparts already
    available from wl_output, in which case the information provided by this
    protocol should be preferred to their equivalent in wl_output. The goal is
    to move the desktop specific concepts (such as output location within the
    global compositor space, etc.) out of the core wl_output protocol.

    Warning! The protocol described in this file is experimental and
    backward incompatible changes may be made. Backward compatible
    changes may be added together with the corresponding interface
    version bump.
    Backward incompatible changes are done by bumping the version
    number in the protocol and interface names and resetting the
    interface version. Once the protocol is to be declared stable,
    the 'z' prefix and the version number in the protocol and
    interface names are removed and the interface version number is
    reset.
  </description>

  <interface name="zxdg_output_manager_v1" version="3">
    <description summary="manage xdg_output objects">
      A global factory interface for xdg_output objects.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the xdg_output_manager object">
	Using this request a client can tell the server that it is not
	going to use the xdg_output_manager object anymore.

	Any objects already created through this instance are not affected.
      </description>
    </request>

    <request name="get_xdg_output">
      <description summary="create an xdg output from a wl_output">
	This creates a new xdg_output object for the given wl_output.
      </description>
      <arg name="id" type="new_id" interface="zxdg_output_v1"/>
      <arg name="output" type="object" interface="wl_output"/>
    </request>
  </interface>

  <interface name="zxdg_output_v1" version="3">
    <description summary="compositor logical output region">
      An xdg_output describes part of the compositor geometry.

      This typically corresponds to a monitor that displays part of the
      compositor space.

      For objects version 3 onwards, after all xdg_output properties have been
      sent (when the object is created and when properties are updated), a
      wl_output.done event is sent. This allows changes to the output
      properties to be seen as atomic, even if they happen via multiple events.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the xdg_output object">
	Using this request a client can tell the server that it is not
	going to use the xdg_output object anymore.
      </description>
    </request>

    <event name="logical_position">
      <description summary="position of the output within the global compositor space">
	The position event describes the location of the wl_output within
	the global compositor space.

	The logical_position event is sent after creating an xdg_output
	(see xdg_output_manager.get_xdg_output) and whenever the location
	of the output changes within the global compositor space.
      </description>
      <arg name="x" type="int"
	   summary="x position within the global compositor space"/>
      <arg name="y" type="int"
	   summary="y position within the global compositor space"/>
    </event>

    <event name="logical_size">
      <description summary="size of the output in the global compositor space">
	The logical_size event describes the size of the output in the
	global compositor space.

	Most regular Wayland clients should not pay attention to the
	logical size and would rather rely on xdg_shell interfaces.

	Some clients such as Xwayland, however, need this to configure
	their surfaces in the global compositor space as the compositor
	may apply a different scale from what is advertised by the output
	scaling property (to achieve fractional scaling, for example).

	For example, for a wl_output mode 3840×2160 and a scale factor 2:

	- A compositor not scaling the monitor viewport in its compositing space
	  will advertise a logical size of 3840×2160,

	- A compositor scaling the monitor viewport with scale factor 2 will
	  advertise a logical size of 1920×1080,

	- A compositor scaling the monitor viewport using a fractional scale of
	  1.5 will advertise a logical size of 2560×1440.

	For example, for a wl_output mode 1920×1080 and a 90 degree rotation,
	the compositor will advertise a logical size of 1080x1920.

	The logical_size event is sent after creating an xdg_output
	(see xdg_output_manager.get_xdg_output) and whenever the logical
	size of the output changes, either as a result of a change in the
	applied scale or because of a change in the corresponding output
	mode(see wl_output.mode) or transform (see wl_output.transform).
      </description>
      <arg name="width" type="int"
	   summary="width in global compositor space"/>
      <arg name="height" type="int"
	   summary="height in global compositor space"/>
    </event>

    <event name="done" deprecated-since="3">
      <description summary="all information about the output have been sent">
	This event is sent after all other properties of an xdg_output
	have been sent.

	This allows changes to the xdg_output properties to be seen as
	atomic, even if they happen via multiple events.

	For objects version 3 onwards, this event is deprecated. Compositors
	are not required to send it anymore and must send wl_output.done
	instead.
      </description>
    </event>

    <!-- Version 2 additions -->

    <event name="name" since="2">
      <description summary="name of this output">
	Many compositors will assign names to their outputs, show them to the
	user, allow them to be configured by name, etc. The client may wish to
	know this name as well to offer the user similar behaviors.

	The naming convention is compositor defined, but limited to
	alphanumeric characters and dashes (-). Each name is unique among all
	wl_output globals, but if a wl_output global is destroyed the same name
	may be reused later. The names will also remain consistent across
	sessions with the same hardware and software configuration.

	Examples of names include 'HDMI-A-1', 'WL-1', 'X11-1', etc. However, do
	not assume that the name is a reflection of an underlying DRM
	connector, X11 connection, etc.

	The name event is sent after creating an xdg_output (see
	xdg_output_manager.get_xdg_output). This event is only sent once per
	xdg_output, and the name does not change over the lifetime of the
	wl_output global.

        This event is deprecated, instead clients should use wl_output.name.
        Compositors must still support this event.
      </description>
      <arg name="name" type="string" summary="output name"/>
    </event>

    <event name="description" since="2">
      <description summary="human-readable description of this output">
	Many compositors can produce human-readable descriptions of their
	outputs.  The client may wish to know this description as well, to
	communicate the user for various purposes.

	The description is a UTF-8 string with no convention defined for its
	contents. Examples might include 'Foocorp 11" Display' or 'Virtual X11
	output via :1'.

	The description event is sent after creating an xdg_output (see
	xdg_output_manager.get_xdg_output) and whenever the description
	changes. The description is optional, and may not be sent at all.

	For objects of version 2 and lower, this event is only sent once per
	xdg_output, and the description does not change over the lifetime of
	the wl_output global.

	This event is deprecated, instead clients should use
	wl_output.description. Compositors must still support this event.
      </description>
      <arg name="description" type="string" summary="output description"/>
    </event>

  </interface>
</protocol>
