<?xml version="1.0" encoding="UTF-8"?>
<protocol name="xdg_system_bell_v1">
  <copyright>
    Copyright © 2016, 2023 Red Hat

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <interface name="xdg_system_bell_v1" version="1">
    <description summary="system bell">
      This global interface enables clients to ring the system bell.

      Warning! The protocol described in this file is currently in the testing
      phase. Backward compatible changes may be added together with the
      corresponding interface version bump. Backward incompatible changes can
      only be done by creating a new major version of the extension.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the system bell object">
	Notify that the object will no longer be used.
      </description>
    </request>

    <request name="ring">
      <description summary="ring the system bell">
	This requests rings the system bell on behalf of a client. How ringing
	the bell is implemented is up to the compositor. It may be an audible
	sound, a visual feedback of some kind, or any other thing including
	nothing.

        The passed surface should correspond to a toplevel like surface role,
        or be null, meaning the client doesn't have a particular toplevel it
        wants to associate the bell ringing with. See the xdg-shell protocol
        extension for a toplevel like surface role.
      </description>
      <arg name="surface" type="object" interface="wl_surface"
	   allow-null="true" summary="associated surface"/>
    </request>
  </interface>
</protocol>
