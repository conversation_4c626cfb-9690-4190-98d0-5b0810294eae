<?xml version="1.0" encoding="UTF-8"?>
<protocol name="xdg_toplevel_tag_v1">
  <copyright>
    Copyright © 2024 Xaver Hugl

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <interface name="xdg_toplevel_tag_manager_v1" version="1">
    <description summary="protocol for setting toplevel tags">
      In order to make some window properties like position, size,
      "always on top" or user defined rules for window behavior persistent, the
      compositor needs some way to identify windows even after the application
      has been restarted.
      This protocol allows clients to make this possible by setting a tag for
      toplevels.

      Warning! The protocol described in this file is currently in the testing
      phase. Backward compatible changes may be added together with the
      corresponding interface version bump. Backward incompatible changes can
      only be done by creating a new major version of the extension.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy toplevel tag object">
        Destroy this toplevel tag manager object. This request has no other
        effects.
      </description>
    </request>

    <request name="set_toplevel_tag">
      <description summary="set tag">
        Set a tag for a toplevel. The tag may be shown to the user in UI, so
        it's preferable for it to be human readable, but it must be suitable
        for configuration files and should not be translated.
        Suitable tags would for example be "main window", "settings",
        "e-mail composer" or similar.

        The tag does not need to be unique across applications, and the client
        may set the same tag for multiple windows, for example if the user has
        opened the same UI twice. How the potentially resulting conflicts are
        handled is compositor policy.

        The client should set the tag as part of the initial commit on the
        associated toplevel, but it may set it at any time afterwards as well,
        for example if the purpose of the toplevel changes.
      </description>
      <arg name="toplevel" type="object" interface="xdg_toplevel"/>
      <arg name="tag" type="string" summary="untranslated tag"/>
    </request>

    <request name="set_toplevel_description">
      <description summary="set description">
        Set a description for a toplevel. This description may be shown to the
        user in UI or read by a screen reader for accessibility purposes, and
        should be translated.
        It is recommended to make the description the translation of the tag.

        The client should set the description as part of the initial commit on
        the associated toplevel, but it may set it at any time afterwards as
        well, for example if the purpose of the toplevel changes.
      </description>
      <arg name="toplevel" type="object" interface="xdg_toplevel"/>
      <arg name="description" type="string" summary="translated description"/>
    </request>
  </interface>

</protocol>
