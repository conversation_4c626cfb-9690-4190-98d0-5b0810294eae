<?xml version="1.0" encoding="UTF-8"?>
<protocol name="ext_idle_notify_v1">
  <copyright>
    Copyright © 2015 <PERSON>
    Copyright © 2022 Simon Ser

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <interface name="ext_idle_notifier_v1" version="2">
    <description summary="idle notification manager">
      This interface allows clients to monitor user idle status.

      After binding to this global, clients can create ext_idle_notification_v1
      objects to get notified when the user is idle for a given amount of time.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the manager">
        Destroy the manager object. All objects created via this interface
        remain valid.
      </description>
    </request>

    <request name="get_idle_notification">
      <description summary="create a notification object">
        Create a new idle notification object.

        The notification object has a minimum timeout duration and is tied to a
        seat. The client will be notified if the seat is inactive for at least
        the provided timeout. See ext_idle_notification_v1 for more details.

        A zero timeout is valid and means the client wants to be notified as
        soon as possible when the seat is inactive.
      </description>
      <arg name="id" type="new_id" interface="ext_idle_notification_v1"/>
      <arg name="timeout" type="uint" summary="minimum idle timeout in msec"/>
      <arg name="seat" type="object" interface="wl_seat"/>
    </request>

    <!-- Version 2 additions -->

    <request name="get_input_idle_notification" since="2">
      <description summary="create a notification object">
        Create a new idle notification object to track input from the
        user, such as keyboard and mouse movement. Because this object is
        meant to track user input alone, it ignores idle inhibitors.

        The notification object has a minimum timeout duration and is tied to a
        seat. The client will be notified if the seat is inactive for at least
        the provided timeout. See ext_idle_notification_v1 for more details.

        A zero timeout is valid and means the client wants to be notified as
        soon as possible when the seat is inactive.
      </description>
      <arg name="id" type="new_id" interface="ext_idle_notification_v1"/>
      <arg name="timeout" type="uint" summary="minimum idle timeout in msec"/>
      <arg name="seat" type="object" interface="wl_seat"/>
    </request>
    
  </interface>

  <interface name="ext_idle_notification_v1" version="2">
    <description summary="idle notification">
      This interface is used by the compositor to send idle notification events
      to clients.

      Initially the notification object is not idle. The notification object
      becomes idle when no user activity has happened for at least the timeout
      duration, starting from the creation of the notification object. User
      activity may include input events or a presence sensor, but is
      compositor-specific.

      How this notification responds to idle inhibitors depends on how
      it was constructed. If constructed from the
      get_idle_notification request, then if an idle inhibitor is
      active (e.g. another client has created a zwp_idle_inhibitor_v1
      on a visible surface), the compositor must not make the
      notification object idle. However, if constructed from the
      get_input_idle_notification request, then idle inhibitors are
      ignored, and only input from the user, e.g. from a keyboard or
      mouse, counts as activity.

      When the notification object becomes idle, an idled event is sent. When
      user activity starts again, the notification object stops being idle,
      a resumed event is sent and the timeout is restarted.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the notification object">
        Destroy the notification object.
      </description>
    </request>

    <event name="idled">
      <description summary="notification object is idle">
        This event is sent when the notification object becomes idle.

        It's a compositor protocol error to send this event twice without a
        resumed event in-between.
      </description>
    </event>

    <event name="resumed">
      <description summary="notification object is no longer idle">
        This event is sent when the notification object stops being idle.

        It's a compositor protocol error to send this event twice without an
        idled event in-between. It's a compositor protocol error to send this
        event prior to any idled event.
      </description>
    </event>
  </interface>
</protocol>
