<?xml version="1.0" encoding="UTF-8"?>
<protocol name="commit_timing_v1">

  <copyright>
    Copyright © 2023 Valve Corporation

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <interface name="wp_commit_timing_manager_v1" version="1">
    <description summary="commit timing">
      When a compositor latches on to new content updates it will check for
      any number of requirements of the available content updates (such as
      fences of all buffers being signalled) to consider the update ready.

      This protocol provides a method for adding a time constraint to surface
      content. This constraint indicates to the compositor that a content
      update should be presented as closely as possible to, but not before,
      a specified time.

      This protocol does not change the Wayland property that content
      updates are applied in the order they are received, even when some
      content updates contain timestamps and others do not.

      To provide timestamps, this global factory interface must be used to
      acquire a wp_commit_timing_v1 object for a surface, which may then be
      used to provide timestamp information for commits.

      Warning! The protocol described in this file is currently in the testing
      phase. Backward compatible changes may be added together with the
      corresponding interface version bump. Backward incompatible changes can
      only be done by creating a new major version of the extension.
    </description>
    <enum name="error">
      <entry name="commit_timer_exists" value="0"
             summary="commit timer already exists for surface"/>
    </enum>

    <request name="destroy" type="destructor">
      <description summary="unbind from the commit timing interface">
        Informs the server that the client will no longer be using
        this protocol object. Existing objects created by this object
        are not affected.
      </description>
    </request>

    <request name="get_timer">
      <description summary="request commit timer interface for surface">
        Establish a timing controller for a surface.

        Only one commit timer can be created for a surface, or a
        commit_timer_exists protocol error will be generated.
      </description>
      <arg name="id" type="new_id" interface="wp_commit_timer_v1"/>
      <arg name="surface" type="object" interface="wl_surface"/>
    </request>
  </interface>

  <interface name="wp_commit_timer_v1" version="1">
    <description summary="Surface commit timer">
      An object to set a time constraint for a content update on a surface.
    </description>

    <enum name="error">
      <entry name="invalid_timestamp" value="0"
             summary="timestamp contains an invalid value"/>
      <entry name="timestamp_exists" value="1"
             summary="timestamp exists"/>
      <entry name="surface_destroyed" value="2"
             summary="the associated surface no longer exists"/>
    </enum>

    <request name="set_timestamp">
      <description summary="Specify time the following commit takes effect">
        Provide a timing constraint for a surface content update.

        A set_timestamp request may be made before a wl_surface.commit to
        tell the compositor that the content is intended to be presented
        as closely as possible to, but not before, the specified time.
        The time is in the domain of the compositor's presentation clock.

        An invalid_timestamp error will be generated for invalid tv_nsec.

        If a timestamp already exists on the surface, a timestamp_exists
        error is generated.

        Requesting set_timestamp after the commit_timer object's surface is
        destroyed will generate a "surface_destroyed" error.
      </description>
      <arg name="tv_sec_hi" type="uint"
           summary="high 32 bits of the seconds part of target time"/>
      <arg name="tv_sec_lo" type="uint"
           summary="low 32 bits of the seconds part of target time"/>
      <arg name="tv_nsec" type="uint"
           summary="nanoseconds part of target time"/>
    </request>

    <request name="destroy" type="destructor">
      <description summary="Destroy the timer">
        Informs the server that the client will no longer be using
        this protocol object.

        Existing timing constraints are not affected by the destruction.
      </description>
    </request>
  </interface>
</protocol>
