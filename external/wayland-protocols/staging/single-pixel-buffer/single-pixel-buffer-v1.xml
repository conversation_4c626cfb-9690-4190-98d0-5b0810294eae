<?xml version="1.0" encoding="UTF-8"?>
<protocol name="single_pixel_buffer_v1">
  <copyright>
    Copyright © 2022 Simon Ser

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <description summary="single pixel buffer factory">
    This protocol extension allows clients to create single-pixel buffers.

    Compositors supporting this protocol extension should also support the
    viewporter protocol extension. Clients may use viewporter to scale a
    single-pixel buffer to a desired size.

    Warning! The protocol described in this file is currently in the testing
    phase. Backward compatible changes may be added together with the
    corresponding interface version bump. Backward incompatible changes can
    only be done by creating a new major version of the extension.
  </description>

  <interface name="wp_single_pixel_buffer_manager_v1" version="1">
    <description summary="global factory for single-pixel buffers">
      The wp_single_pixel_buffer_manager_v1 interface is a factory for
      single-pixel buffers.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the manager">
        Destroy the wp_single_pixel_buffer_manager_v1 object.

        The child objects created via this interface are unaffected.
      </description>
    </request>

    <request name="create_u32_rgba_buffer">
      <description summary="create a 1×1 buffer from 32-bit RGBA values">
        Create a single-pixel buffer from four 32-bit RGBA values.

        Unless specified in another protocol extension, the RGBA values use
        pre-multiplied alpha.

        The width and height of the buffer are 1.
      </description>
      <arg name="id" type="new_id" interface="wl_buffer"/>
      <arg name="r" type="uint" summary="value of the buffer's red channel"/>
      <arg name="g" type="uint" summary="value of the buffer's green channel"/>
      <arg name="b" type="uint" summary="value of the buffer's blue channel"/>
      <arg name="a" type="uint" summary="value of the buffer's alpha channel"/>
    </request>
  </interface>
</protocol>
