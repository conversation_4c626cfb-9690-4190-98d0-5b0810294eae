<?xml version="1.0" encoding="UTF-8"?>
<protocol name="ext_background_effect_v1">
  <copyright>
    Copyright (C) 2015 <PERSON>
    Copyright (C) 2015 <PERSON>
    Copyright (C) 2020 Vlad <PERSON>
    Copyright (C) 2024 Xaver Hugl

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <interface name="ext_background_effect_manager_v1" version="1">
    <description summary="background effect factory">
      This protocol provides a way to improve visuals of translucent surfaces
      by applying effects like blur to the background behind them.

      The capabilities are send when the global is bound, and every time they
      change. Note that when the capability goes away, the corresponding effect
      is no longer applied by the compositor, even if it was set before.

      Warning! The protocol described in this file is currently in the testing
      phase. Backward compatible changes may be added together with the
      corresponding interface version bump. Backward incompatible changes can
      only be done by creating a new major version of the extension.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the background effect manager">
        Informs the server that the client will no longer be using this
        protocol object. Existing objects created by this object are not
        affected.
      </description>
    </request>

    <enum name="error">
      <entry name="background_effect_exists" value="0"
             summary="the surface already has a background effect object"/>
    </enum>

    <enum name="capability" bitfield="true">
      <entry name="blur" value="0"
             summary="the compositor supports applying blur"/>
    </enum>

    <event name="capabilities">
      <description summary="capabilities of the compositor"/>
      <arg name="flags" type="uint" enum="capability"/>
    </event>

    <request name="get_background_effect">
      <description summary="get a background effects object">
        Instantiate an interface extension for the given wl_surface to add
        effects like blur for the background behind it.

        If the given wl_surface already has a ext_background_effect_surface_v1
        object associated, the background_effect_exists protocol error will be
        raised.
      </description>
      <arg name="id" type="new_id" interface="ext_background_effect_surface_v1"
           summary="the new ext_background_effect_surface_v1 object"/>
      <arg name="surface" type="object" interface="wl_surface"
           summary="the surface"/>
    </request>
  </interface>

  <interface name="ext_background_effect_surface_v1" version="1">
    <description summary="background effects for a surface">
      The background effect object provides a way to specify a region behind
      a surface that should have background effects like blur applied.

      If the wl_surface associated with the ext_background_effect_surface_v1
      object has been destroyed, this object becomes inert.
    </description>

    <request name="destroy" type="destructor">
      <description summary="release the blur object">
        Informs the server that the client will no longer be using this protocol
        object. The effect regions will be removed on the next commit.
      </description>
    </request>

    <enum name="error">
      <entry name="surface_destroyed" value="0"
             summary="the associated surface has been destroyed"/>
    </enum>

    <request name="set_blur_region">
      <description summary="set blur region">
        This request sets the region of the surface that will have its
        background blurred.

        The blur region is specified in the surface-local coordinates, and
        clipped by the compositor to the surface size.

        The initial value for the blur region is empty. Setting the pending
        blur region has copy semantics, and the wl_region object can be
        destroyed immediately. A NULL wl_region removes the effect.

        The blur region is double-buffered state, and will be applied on
        the next wl_surface.commit.

        The blur algorithm is subject to compositor policies.

        If the associated surface has been destroyed, the surface_destroyed
        error will be raised.
      </description>
      <arg name="region" type="object" interface="wl_region" allow-null="true"
           summary="blur region of the surface"/>
    </request>

  </interface>
</protocol>
