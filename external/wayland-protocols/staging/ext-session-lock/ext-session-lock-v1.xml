<?xml version="1.0" encoding="UTF-8"?>
<protocol name="ext_session_lock_v1">
  <copyright>
    Copyright 2021 Isaac Freund

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in
    all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
    THE SOFTWARE.
  </copyright>

  <description summary="secure session locking with arbitrary graphics">
    This protocol allows for a privileged Wayland client to lock the session
    and display arbitrary graphics while the session is locked.

    The compositor may choose to restrict this protocol to a special client
    launched by the compositor itself or expose it to all privileged clients,
    this is compositor policy.

    The client is responsible for performing authentication and informing the
    compositor when the session should be unlocked. If the client dies while
    the session is locked the session remains locked, possibly permanently
    depending on compositor policy.

    The key words "must", "must not", "required", "shall", "shall not",
    "should", "should not", "recommended",  "may", and "optional" in this
    document are to be interpreted as described in IETF RFC 2119.

    Warning! The protocol described in this file is currently in the
    testing phase. Backward compatible changes may be added together with
    the corresponding interface version bump. Backward incompatible changes
    can only be done by creating a new major version of the extension.
  </description>

  <interface name="ext_session_lock_manager_v1" version="1">
    <description summary="used to lock the session">
      This interface is used to request that the session be locked.
    </description>

    <request name="destroy" type="destructor">
      <description summary="destroy the session lock manager object">
        This informs the compositor that the session lock manager object will
        no longer be used. Existing objects created through this interface
        remain valid.
      </description>
    </request>

    <request name="lock">
      <description summary="attempt to lock the session">
        This request creates a session lock and asks the compositor to lock the
        session. The compositor will send either the ext_session_lock_v1.locked
        or ext_session_lock_v1.finished event on the created object in
        response to this request.
      </description>
      <arg name="id" type="new_id" interface="ext_session_lock_v1"/>
    </request>
  </interface>

  <interface name="ext_session_lock_v1" version="1">
    <description summary="manage lock state and create lock surfaces">
      In response to the creation of this object the compositor must send
      either the locked or finished event.

      The locked event indicates that the session is locked. This means
      that the compositor must stop rendering and providing input to normal
      clients. Instead the compositor must blank all outputs with an opaque
      color such that their normal content is fully hidden.

      The only surfaces that should be rendered while the session is locked
      are the lock surfaces created through this interface and optionally,
      at the compositor's discretion, special privileged surfaces such as
      input methods or portions of desktop shell UIs.

      The locked event must not be sent until a new "locked" frame (either
      from a session lock surface or the compositor blanking the output) has
      been presented on all outputs and no security sensitive normal/unlocked
      content is possibly visible.

      The finished event should be sent immediately on creation of this
      object if the compositor decides that the locked event will not be sent.

      The compositor may wait for the client to create and render session lock
      surfaces before sending the locked event to avoid displaying intermediate
      blank frames. However, it must impose a reasonable time limit if
      waiting and send the locked event as soon as the hard requirements
      described above can be met if the time limit expires. Clients should
      immediately create lock surfaces for all outputs on creation of this
      object to make this possible.

      This behavior of the locked event is required in order to prevent
      possible race conditions with clients that wish to suspend the system
      or similar after locking the session. Without these semantics, clients
      triggering a suspend after receiving the locked event would race with
      the first "locked" frame being presented and normal/unlocked frames
      might be briefly visible as the system is resumed if the suspend
      operation wins the race.

      If the client dies while the session is locked, the compositor must not
      unlock the session in response. It is acceptable for the session to be
      permanently locked if this happens. The compositor may choose to continue
      to display the lock surfaces the client had mapped before it died or
      alternatively fall back to a solid color, this is compositor policy.

      Compositors may also allow a secure way to recover the session, the
      details of this are compositor policy. Compositors may allow a new
      client to create a ext_session_lock_v1 object and take responsibility
      for unlocking the session, they may even start a new lock client
      instance automatically.
    </description>

    <enum name="error">
      <entry name="invalid_destroy" value="0"
        summary="attempted to destroy session lock while locked"/>
      <entry name="invalid_unlock" value="1"
        summary="unlock requested but locked event was never sent"/>
      <entry name="role" value="2"
        summary="given wl_surface already has a role"/>
      <entry name="duplicate_output" value="3"
        summary="given output already has a lock surface"/>
      <entry name="already_constructed" value="4"
        summary="given wl_surface has a buffer attached or committed"/>
    </enum>

    <request name="destroy" type="destructor">
      <description summary="destroy the session lock">
        This informs the compositor that the lock object will no longer be
        used. Existing objects created through this interface remain valid.

        After this request is made, lock surfaces created through this object
        should be destroyed by the client as they will no longer be used by
        the compositor.

        It is a protocol error to make this request if the locked event was
        sent, the unlock_and_destroy request must be used instead.
      </description>
    </request>

    <event name="locked">
      <description summary="session successfully locked">
        This client is now responsible for displaying graphics while the
        session is locked and deciding when to unlock the session.

        The locked event must not be sent until a new "locked" frame has been
        presented on all outputs and no security sensitive normal/unlocked
        content is possibly visible.

        If this event is sent, making the destroy request is a protocol error,
        the lock object must be destroyed using the unlock_and_destroy request.
      </description>
    </event>

    <event name="finished">
      <description summary="the session lock object should be destroyed">
        The compositor has decided that the session lock should be destroyed
        as it will no longer be used by the compositor. Exactly when this
        event is sent is compositor policy, but it must never be sent more
        than once for a given session lock object.

        This might be sent because there is already another ext_session_lock_v1
        object held by a client, or the compositor has decided to deny the
        request to lock the session for some other reason. This might also
        be sent because the compositor implements some alternative, secure
        way to authenticate and unlock the session.

        The finished event should be sent immediately on creation of this
        object if the compositor decides that the locked event will not
        be sent.

        If the locked event is sent on creation of this object the finished
        event may still be sent at some later time in this object's
        lifetime. This is compositor policy.

        Upon receiving this event, the client should make either the destroy
        request or the unlock_and_destroy request, depending on whether or
        not the locked event was received on this object.
      </description>
    </event>

    <request name="get_lock_surface">
      <description summary="create a lock surface for a given output">
        The client is expected to create lock surfaces for all outputs
        currently present and any new outputs as they are advertised. These
        won't be displayed by the compositor unless the lock is successful
        and the locked event is sent.

        Providing a wl_surface which already has a role or already has a buffer
        attached or committed is a protocol error, as is attaching/committing
        a buffer before the first ext_session_lock_surface_v1.configure event.

        Attempting to create more than one lock surface for a given output
        is a duplicate_output protocol error.
      </description>
      <arg name="id" type="new_id" interface="ext_session_lock_surface_v1"/>
      <arg name="surface" type="object" interface="wl_surface"/>
      <arg name="output" type="object" interface="wl_output"/>
    </request>

    <request name="unlock_and_destroy" type="destructor">
      <description summary="unlock the session, destroying the object">
        This request indicates that the session should be unlocked, for
        example because the user has entered their password and it has been
        verified by the client.

        This request also informs the compositor that the lock object will
        no longer be used and should be destroyed. Existing objects created
        through this interface remain valid.

        After this request is made, lock surfaces created through this object
        should be destroyed by the client as they will no longer be used by
        the compositor.

        It is a protocol error to make this request if the locked event has
        not been sent. In that case, the lock object must be destroyed using
        the destroy request.

        Note that a correct client that wishes to exit directly after unlocking
        the session must use the wl_display.sync request to ensure the server
        receives and processes the unlock_and_destroy request. Otherwise
        there is no guarantee that the server has unlocked the session due
        to the asynchronous nature of the Wayland protocol. For example,
        the server might terminate the client with a protocol error before
        it processes the unlock_and_destroy request.
      </description>
    </request>
  </interface>

  <interface name="ext_session_lock_surface_v1" version="1">
    <description summary="a surface displayed while the session is locked">
      The client may use lock surfaces to display a screensaver, render a
      dialog to enter a password and unlock the session, or however else it
      sees fit.

      On binding this interface the compositor will immediately send the
      first configure event. After making the ack_configure request in
      response to this event the client should attach and commit the first
      buffer. Committing the surface before acking the first configure is a
      protocol error. Committing the surface with a null buffer at any time
      is a protocol error.

      The compositor is free to handle keyboard/pointer focus for lock
      surfaces however it chooses. A reasonable way to do this would be to
      give the first lock surface created keyboard focus and change keyboard
      focus if the user clicks on other surfaces.
    </description>

    <enum name="error">
      <entry name="commit_before_first_ack" value="0"
        summary="surface committed before first ack_configure request"/>
      <entry name="null_buffer" value="1"
        summary="surface committed with a null buffer"/>
      <entry name="dimensions_mismatch" value="2"
        summary="failed to match ack'd width/height"/>
      <entry name="invalid_serial" value="3"
        summary="serial provided in ack_configure is invalid"/>
    </enum>

    <request name="destroy" type="destructor">
      <description summary="destroy the lock surface object">
        This informs the compositor that the lock surface object will no
        longer be used.

        It is recommended for a lock client to destroy lock surfaces if
        their corresponding wl_output global is removed.

        If a lock surface on an active output is destroyed before the
        ext_session_lock_v1.unlock_and_destroy event is sent, the compositor
        must fall back to rendering a solid color.
      </description>
    </request>

    <request name="ack_configure">
      <description summary="ack a configure event">
        When a configure event is received, if a client commits the surface
        in response to the configure event, then the client must make an
        ack_configure request sometime before the commit request, passing
        along the serial of the configure event.

        If the client receives multiple configure events before it can
        respond to one, it only has to ack the last configure event.

        A client is not required to commit immediately after sending an
        ack_configure request - it may even ack_configure several times
        before its next surface commit.

        A client may send multiple ack_configure requests before committing,
        but only the last request sent before a commit indicates which
        configure event the client really is responding to.

        Sending an ack_configure request consumes the configure event
        referenced by the given serial, as well as all older configure events
        sent on this object.

        It is a protocol error to issue multiple ack_configure requests
        referencing the same configure event or to issue an ack_configure
        request referencing a configure event older than the last configure
        event acked for a given lock surface.
      </description>
      <arg name="serial" type="uint" summary="serial from the configure event"/>
    </request>

    <event name="configure">
      <description summary="the client should resize its surface">
        This event is sent once on binding the interface and may be sent again
        at the compositor's discretion, for example if output geometry changes.

        The width and height are in surface-local coordinates and are exact
        requirements. Failing to match these surface dimensions in the next
        commit after acking a configure is a protocol error.
      </description>
      <arg name="serial" type="uint" summary="serial for use in ack_configure"/>
      <arg name="width" type="uint"/>
      <arg name="height" type="uint"/>
    </event>
  </interface>
</protocol>
