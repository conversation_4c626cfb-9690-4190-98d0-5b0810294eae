// MIT/Apache2 License

//! Generator for Wayland code.

mod wgen;

use std::env;
use std::path::PathBuf;
use std::process;

fn usage() -> ! {
    let argv0: PathBuf = env::args_os().next().unwrap().into();
    let program_name = argv0.file_name().unwrap_or(argv0.as_os_str());
    eprintln!(
        "usage: {} <input xml file> <output folder>",
        program_name.to_string_lossy()
    );
    process::exit(1)
}

fn main() {
    if let Err(err) = entry() {
        eprintln!("a fatal error occurred: {err}");
        process::exit(1);
    }
}

fn entry() -> anyhow::Result<()> {
    // Get input arguments.
    let mut args = env::args_os().skip(1);
    let input_xml = args.next().map(PathBuf::from).ok_or_else(usage)?;
    let output_folder = args.next().map(PathBuf::from).ok_or_else(usage)?;

    // Generate the code.
    let protocol_name = wgen::generate(&input_xml, &output_folder)?;
    println!("{protocol_name}");

    Ok(())
}
