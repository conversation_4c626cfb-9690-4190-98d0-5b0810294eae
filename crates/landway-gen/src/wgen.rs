// MIT/Apache2 License

//! Generator for Wayland protocol code.

use std::fmt;
use std::fs;
use std::io::Read;
use std::path::Path;

use anyhow::{Context, Result};
use heck::{AsShoutySnakeCase, AsUpperCamelCase};
use roxmltree::{Document, Node};

macro_rules! out {
    ($out:expr, $($args:tt)+) => {{
        use std::fmt::Write as _;
        write!(&mut $out, $($args)+).unwrap();
    }}
}

macro_rules! outln {
    ($out:expr) => {{
        out!($out, "\n")
    }};
    ($out:expr, $($args:tt)+) => {{
        use std::fmt::Write as _;
        writeln!(&mut $out, $($args)+).unwrap();
    }}
}

/// Generate Wayland code and store it at the provided path.
///
/// Returns the protocol's name.
pub fn generate(input_xml: &Path, output_folder: &Path) -> Result<String> {
    // Read in the file and parse it as XML.
    let mut xml = String::new();
    fs::File::open(input_xml)
        .with_context(|| format!("failed to open {input_xml:?}"))?
        .read_to_string(&mut xml)
        .with_context(|| format!("failed to read {input_xml:?}"))?;
    let doc = Document::parse(&xml).with_context(|| format!("failed to parse {input_xml:?}"))?;

    // Read the root element and extract the name.
    let root = doc.root_element();
    let name = root
        .attribute("name")
        .context("could not find 'name' attribute")?;

    // Begin our writer.
    let mut out = Writer::default();

    // Write the preamble.
    outln!(out, "// MIT/Apache2 License");
    outln!(
        out,
        "// This file is automatically generated! Do not edit this file manually."
    );
    outln!(out, "// Please see the landway-gen code for more info.");
    outln!(out);
    outln!(out, "//! The `{name}` protocol.");
    outln!(out);

    // Fixes for Clippy warnings that occur otherwise.
    outln!(out, "#![allow(");
    out.with_indent(|mut out| {
        outln!(out, "clippy::doc_lazy_continuation,");
        outln!(out, "clippy::doc_markdown,");
        outln!(out, "clippy::match_single_binding,");
        outln!(out, "clippy::missing_errors_doc,");
        outln!(out, "clippy::missing_panics_doc,");
        outln!(out, "clippy::unreadable_literal,");
        outln!(out, "clippy::redundant_closure_for_method_calls,");
        outln!(out, "clippy::too_many_lines,");
        outln!(out, "unused_imports,");
        outln!(out, "unused_mut");
    });
    outln!(out, ")]");
    outln!(out);

    outln!(out, "#[allow(clippy::wildcard_imports)]");
    outln!(out, "use super::__all::*;");
    outln!(out);

    // Begin processing nodes.
    for child in root.children() {
        handle_elem(&mut out, child)?;
    }

    // Write out to a file.
    let outpath = output_folder.join(format!("{name}.rs"));
    fs::write(&outpath, out.buffer).with_context(|| format!("failed to write {outpath:?}"))?;

    Ok(name.to_string())
}

/// Process an XML element.
fn handle_elem(mut out: &mut Writer, elem: Node<'_, '_>) -> Result<()> {
    match elem.tag_name().name() {
        "copyright" => {
            // Wayland copyright. We legally need to emit it to the file.
            outln!(out, "// Original Wayland copyright information");
            outln!(out, "// -------------------------------------------------");

            for line in elem.text().context("no text")?.lines() {
                let line = line.trim();
                outln!(out, "// {line}");
            }

            outln!(out, "// -------------------------------------------------");
            outln!(out);
        }

        "interface" => {
            // Create a module for this element.
            let name = elem.attribute("name").context("no 'name' attribute")?;
            outln!(out, "pub mod {name} {{");
            out.indent();

            // Look for the "description" member.
            emit_description(out, name, &elem, false)?;

            // Emit the dependency imports.
            outln!(out, "use crate::Proxy;");
            outln!(out, "use std::ffi::CStr;");
            outln!(out, "use std::fmt;");
            outln!(out, "use std::io;");
            outln!(out, "use std::os::unix::io::AsFd;");
            outln!(out);

            // Write the Proxy wrapper.
            outln!(out, "/// Wrapper around the `{name}` interface.");
            outln!(out, "///");
            outln!(
                out,
                "/// See [top-level documentation](super::mod.rs) for more info."
            );
            outln!(out, "pub struct {}(Proxy);", AsUpperCamelCase(name));
            outln!(out);

            emit_trait_impl(out, name)?;

            outln!(out, "impl {} {{", AsUpperCamelCase(name));
            out.indent();
            emit_requests(out, &elem)?;
            emit_add_listener(out, &elem)?;
            outln!(out);

            // Write out the known version.
            let version = elem.attribute("version").unwrap_or("0");
            outln!(out, "/// The latest known version for this proxy.");
            outln!(out, "pub const VERSION: u32 = {version};");
            outln!(out);
            outln!(out, "/// The interface for this proxy.");
            outln!(
                out,
                "pub const INTERFACE: &'static crate::Interface = &INTERFACE;"
            );

            out.dedent();
            outln!(out, "}}");
            outln!(out);

            // Write out the event.
            emit_event_enum(out, &elem)?;

            // Write out the interface.
            emit_iface_impl(out, name, version, &elem)?;

            // Write out any enums.
            for enum_ in elem.children().filter(|n| n.tag_name().name() == "enum") {
                emit_enum(out, &enum_)?;
            }

            out.dedent();
            outln!(out, "}}");
            outln!(out);

            // Emit a "use" statement.
            outln!(out, "pub use self::{name}::{};", AsUpperCamelCase(name));
            outln!(out);
        }

        "" => {}

        name => eprintln!("unknown top-level element: {name:?}"),
    }

    Ok(())
}

/// Emit basic proxy trait implementations.
fn emit_trait_impl(mut out: &mut Writer, name: &str) -> Result<()> {
    // Emit `Debug` wrapper.
    outln!(out, "impl fmt::Debug for {} {{", AsUpperCamelCase(name));
    out.indent();
    outln!(
        out,
        "fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {{"
    );
    out.indent();
    outln!(
        out,
        "f.debug_tuple(\"{}\").field(&self.0).finish()",
        AsUpperCamelCase(name)
    );
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    // Emit implementation for `From<Proxy>`.
    outln!(out, "impl From<Proxy> for {} {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "fn from(proxy: Proxy) -> Self {{");
    out.indent();
    outln!(out, "{}(proxy)", AsUpperCamelCase(name));
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    // Emit `From<{}> for Proxy`
    outln!(out, "impl From<{}> for Proxy {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "fn from(proxy: {}) -> Self {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "proxy.0");
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    // Emit `AsRef` and `AsMut` for `Proxy`.
    outln!(out, "impl AsRef<Proxy> for {} {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "fn as_ref(&self) -> &Proxy {{");
    out.indent();
    outln!(out, "&self.0");
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    outln!(out, "impl AsMut<Proxy> for {} {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "fn as_mut(&mut self) -> &mut Proxy {{");
    out.indent();
    outln!(out, "&mut self.0");
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    outln!(out, "impl {} {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "/// Get a reference to the underlying proxy.");
    outln!(out, "#[inline]");
    outln!(out, "#[must_use]");
    outln!(out, "pub fn as_proxy(&self) -> &Proxy {{");
    out.indent();
    outln!(out, "&self.0");
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    Ok(())
}

/// Emit an enum containing some values.
fn emit_enum(mut out: &mut Writer, node: &Node<'_, '_>) -> Result<()> {
    let name = node.attribute("name").context("no 'name' attribute")?;

    emit_description(out, name, node, true)?;
    outln!(out, "#[allow(missing_docs)]");
    outln!(out, "#[derive(Debug, Copy, Clone, PartialEq, Eq)]");
    outln!(out, "pub struct {}(u32);", AsUpperCamelCase(name));
    outln!(out);

    outln!(out, "impl {} {{", AsUpperCamelCase(name));
    out.indent();

    for entry in node.children().filter(|n| n.tag_name().name() == "entry") {
        let entry_name = entry.attribute("name").context("no 'name' attribute")?;
        let value = entry.attribute("value").context("no 'value' attribute")?;
        let summary = entry.attribute("summary").unwrap_or("");

        outln!(out, "/// {entry_name} - {summary}");
        outln!(
            out,
            "pub const {0}: {1} = {1}({2});",
            AsShoutySnakeCase(sanitize(entry_name)),
            AsUpperCamelCase(name),
            value
        );
        outln!(out);
    }

    out.dedent();
    outln!(out, "}}");
    outln!(out);

    // Add From<u32> impl
    outln!(out, "impl From<u32> for {} {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "fn from(value: u32) -> Self {{");
    out.indent();
    outln!(out, "{}(value)", AsUpperCamelCase(name));
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    // Add From<i32> impl
    outln!(out, "impl From<i32> for {} {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "#[allow(clippy::cast_sign_loss)]");
    outln!(out, "fn from(value: i32) -> Self {{");
    out.indent();
    outln!(out, "{}::from(value as u32)", AsUpperCamelCase(name));
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    // Add From<enum> for u32 impl
    outln!(out, "impl From<{}> for u32 {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "fn from(value: {}) -> Self {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "value.0");
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    // Add From<enum> for i32 impl
    outln!(out, "impl From<{}> for i32 {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "#[allow(clippy::cast_possible_wrap)]");
    outln!(out, "fn from(value: {}) -> Self {{", AsUpperCamelCase(name));
    out.indent();
    outln!(out, "value.0 as i32");
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    Ok(())
}

/// Emit the request functions.
fn emit_requests(mut out: &mut Writer, node: &Node<'_, '_>) -> Result<()> {
    for (i, request) in node
        .children()
        .filter(|n| n.tag_name().name() == "request")
        .enumerate()
    {
        let name = sanitize(request.attribute("name").context("no 'name' attribute")?);

        // Emit function signature.
        let mut new_id = None;
        outln!(out, "pub fn {name}(");
        out.with_indent(|mut out| {
            outln!(out, "&mut self,");
            for arg in arg_iter(&request) {
                if let Some(enum_name) = arg.enum_name {
                    outln!(out, "{}: {},", arg.name, EnumName(enum_name));
                    continue;
                }

                match arg.ty {
                    Ty::Int => outln!(out, "{}: i32,", arg.name),
                    Ty::UInt => outln!(out, "{}: u32, ", arg.name),
                    Ty::Fixed => outln!(out, "{}: impl Into<Fixed>, ", arg.name),
                    Ty::String => {
                        if arg.allow_null {
                            outln!(out, "{}: Option<&CStr>, ", arg.name);
                        } else {
                            outln!(out, "{}: &CStr, ", arg.name);
                        }
                    }
                    Ty::NewId => {
                        new_id = Some(arg.interface);
                        if arg.interface.is_none() {
                            outln!(out, "interface: &'static crate::Interface,");
                            outln!(out, "version: u32,");
                        }
                        outln!(out, "event_queue: &crate::EventQueue,");
                    }
                    Ty::Object => {
                        out!(out, "{}: ", arg.name);
                        if arg.allow_null {
                            out!(out, "Option<");
                        }

                        // Emit interface if it's there.
                        if let Some(interface) = arg.interface {
                            // TODO: Proper interface names.
                            out!(out, "&{}", InterfaceName(interface));
                        } else {
                            out!(out, "&Proxy");
                        }

                        if arg.allow_null {
                            outln!(out, ">, ");
                        } else {
                            outln!(out, ", ");
                        }
                    }
                    Ty::Array => outln!(out, "{}: &[u8], ", arg.name),
                    Ty::FileDescriptor => outln!(out, "{}: impl AsFd, ", arg.name),
                }
            }
        });
        out!(out, ") -> io::Result<");
        match new_id {
            None => out!(out, "()"),

            Some(Some(interface)) => {
                out!(out, "{}", InterfaceName(interface));
            }

            Some(None) => {
                out!(out, "Proxy");
            }
        }
        outln!(out, "> {{");

        out.indent();

        // Emit description if it's there.
        emit_description(out, name, &request, false)?;

        outln!(out, "const OPCODE: u32 = {i};");
        outln!(out);

        // Convert all enums.
        for arg in arg_iter(&request) {
            if arg.enum_name.is_some() {
                outln!(out, "let {0} = {0}.into();", sanitize(arg.name));
            }
        }

        // Create the arguments.
        outln!(out, "crate::args!(args = ");
        out.indent();
        for arg in arg_iter(&request) {
            match arg.ty {
                Ty::Int => outln!(out, "(Int32, {}),", arg.name),
                Ty::UInt => outln!(out, "(UInt32, {}),", arg.name),
                Ty::Fixed => outln!(out, "(Fixed, {}.into()),", arg.name),
                Ty::String => {
                    if arg.allow_null {
                        outln!(out, "(NString, {}),", arg.name);
                    } else {
                        outln!(out, "(String, Some({})),", arg.name);
                    }
                }
                Ty::Object => {
                    if arg.allow_null {
                        outln!(out, "(NObject, {}.map(|x| x.as_proxy())),", arg.name);
                    } else {
                        outln!(out, "(Object, Some({}.as_ref())),", arg.name);
                    }
                }
                Ty::NewId => outln!(out, "(NewId, 0),"),
                Ty::Array => outln!(out, "(Array, {}.as_ref()),", arg.name),
                Ty::FileDescriptor => outln!(out, "(FileDescriptor, {}.as_fd()),", arg.name),
            }
        }
        out.dedent();
        outln!(out, ");");

        // Send the request.
        if new_id.is_some() {
            outln!(out, "let proxy = self.0.send_message_constructor(");
            out.indent();
            outln!(out, "OPCODE,");
            outln!(out, "&args,");
            if let Some(Some(interface)) = new_id {
                outln!(out, "{}::INTERFACE,", InterfaceName(interface));
                outln!(out, "{}::VERSION,", InterfaceName(interface));
            } else {
                outln!(out, "interface,");
                outln!(out, "version,");
            }
            outln!(out, "event_queue",);
            out.dedent();
            outln!(out, ")?;");
        } else {
            outln!(out, "self.0.send_message(OPCODE, &args)?;");
        }

        match new_id {
            None => outln!(out, "Ok(())"),
            Some(None) => outln!(out, "Ok(proxy)"),
            Some(Some(interface)) => outln!(out, "Ok({}::from(proxy))", InterfaceName(interface)),
        }

        out.dedent();
        outln!(out, "}}");
        outln!(out);
    }

    Ok(())
}

/// Emit a function for setting a listener.
fn emit_add_listener(mut out: &mut Writer, node: &Node<'_, '_>) -> Result<()> {
    outln!(out, "/// Set a listener for incoming events.");
    outln!(
        out,
        "pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>"
    );
    outln!(out, "where F: FnMut(&Self, Event<'_>) + 'static");
    outln!(out, "{{");
    out.indent();
    outln!(out, "self.0.add_listener(move |proxy, opcode, incoming| {{");
    out.indent();
    outln!(out, "let proxy: Self = proxy.into();");
    outln!(out, "let mut iter = incoming.iter(proxy.as_ref());");
    outln!(out, "match opcode {{");
    out.indent();

    for (i, event) in node
        .children()
        .filter(|n| n.tag_name().name() == "event")
        .enumerate()
    {
        let name = event.attribute("name").context("no 'name' attribute")?;
        outln!(out, "{i} => {{");
        out.indent();

        // Collect all arguments.
        for arg in arg_iter(&event) {
            outln!(out, "let {} = match iter.next() {{", sanitize(arg.name));
            out.indent();

            let kind = match arg.ty {
                Ty::Int => "Int32",
                Ty::UInt => "UInt32",
                Ty::Fixed => "Float",
                Ty::String => "String",
                Ty::Object => "Object",
                Ty::NewId => "NewId",
                Ty::Array => "Array",
                Ty::FileDescriptor => "FileDescriptor",
            };
            outln!(out, "Some(crate::TypedArgument::{kind}(arg)) => arg,");
            outln!(out, "_ => panic!(\"protocol violation\"),");

            out.dedent();
            outln!(out, "}};");

            // Wrap in a proxy if it's a typed object.
            if let (Ty::Object | Ty::NewId, Some(interface)) = (&arg.ty, arg.interface) {
                outln!(
                    out,
                    "let {0} = {0}.map({1}::from);",
                    sanitize(arg.name),
                    InterfaceName(interface)
                );
            }

            // Unwrap if it's not optional.
            if matches!(arg.ty, Ty::String | Ty::Object | Ty::NewId) && !arg.allow_null {
                outln!(out, "let {0} = {0}.unwrap();", sanitize(arg.name));
            }

            // Add enum wrapper if needed.
            if let Some(enum_name) = arg.enum_name {
                outln!(
                    out,
                    "let {0} = {1}::from({0});",
                    sanitize(arg.name),
                    EnumName(enum_name)
                );
            }

            outln!(out);
        }

        // There should be no other items left.
        outln!(out, "assert!(iter.next().is_none());");

        // Create the event.
        outln!(out, "let event = Event::{} {{", AsUpperCamelCase(name));
        out.indent();

        for arg in arg_iter(&event) {
            match (arg.ty, arg.allow_null) {
                (Ty::Object, false) => outln!(out, "{0}: &{0},", sanitize(arg.name)),
                (Ty::Object, true) => {
                    outln!(out, "{0}: {0}.as_ref(),", sanitize(arg.name))
                }
                (Ty::Array, _) => outln!(out, "{0}: {0}.as_slice(),", sanitize(arg.name)),
                _ => outln!(out, "{},", sanitize(arg.name)),
            }
        }

        out.dedent();
        outln!(out, "}};");
        outln!(out, "listener(&proxy, event);");

        out.dedent();
        outln!(out, "}},");
    }

    outln!(out, "_ => {{ let _ = (&listener, iter); }}");
    out.dedent();
    outln!(out, "}}");
    out.dedent();
    outln!(out, "}})");
    out.dedent();
    outln!(out, "}}");
    outln!(out);

    Ok(())
}

/// Emit an event enum type.
fn emit_event_enum(mut out: &mut Writer, elem: &Node<'_, '_>) -> Result<()> {
    outln!(
        out,
        "/// Sum type containing all possible events for this interface."
    );
    outln!(out, "#[non_exhaustive]");
    outln!(out, "#[derive(Debug)]");
    outln!(out, "pub enum Event<'a> {{");
    out.indent();

    // Eat the lifetime if there isn't one otherwise.
    outln!(out, "#[doc(hidden)]");
    outln!(out, "__Lifetime(std::marker::PhantomData<&'a ()>),");

    for event in elem.children().filter(|n| n.tag_name().name() == "event") {
        let name = event.attribute("name").context("no 'name' attribute")?;
        emit_description(out, name, &event, true)?;
        outln!(out, "{} {{", AsUpperCamelCase(name));
        out.indent();

        // Read each field and add it.
        for arg in arg_iter(&event) {
            // Write out the summary.
            if let Some(summary) = arg.node.attribute("summary") {
                outln!(out, "/// {} - {summary}", arg.name);
            } else {
                outln!(out, "/// {}", arg.name);
            }

            out!(out, "{}: ", sanitize(arg.name));
            if matches!(arg.ty, Ty::Object | Ty::String) && arg.allow_null {
                out!(out, "Option<");
            }

            match (arg.enum_name, &arg.ty) {
                (Some(enum_name), _) => out!(out, "{}", EnumName(enum_name)),
                (_, Ty::Int) => out!(out, "i32"),
                (_, Ty::UInt) => out!(out, "u32"),
                (_, Ty::Fixed) => out!(out, "crate::Fixed"),
                (_, Ty::String) => out!(out, "&'a CStr"),
                (_, Ty::Object | Ty::NewId) => {
                    if let Ty::Object = arg.ty {
                        out!(out, "&'a ");
                    }

                    if let Some(interface) = arg.interface {
                        out!(out, "{}", InterfaceName(interface));
                    } else {
                        out!(out, "Proxy");
                    }
                }
                (_, Ty::Array) => out!(out, "&'a [u8]"),
                (_, Ty::FileDescriptor) => out!(out, "std::os::unix::io::BorrowedFd<'a>"),
            }

            if matches!(arg.ty, Ty::Object | Ty::String) && arg.allow_null {
                out!(out, ">");
            }

            outln!(out, ",");
        }

        out.dedent();
        outln!(out, "}},");
    }

    out.dedent();
    outln!(out, "}}");
    outln!(out);

    Ok(())
}

/// Emit a description.
fn emit_description(
    mut out: &mut Writer,
    name: &str,
    elem: &Node<'_, '_>,
    outer: bool,
) -> Result<()> {
    let comment = if outer { "/// " } else { "//! " };

    let description = elem
        .children()
        .find(|n| n.tag_name().name() == "description");
    if let Some(description) = description {
        let summary = description.attribute("summary").unwrap_or("").trim();
        outln!(out, "{comment} {name}: {summary}");

        // Also emit the description if possible.
        if let Some(text) = description.text() {
            outln!(out, "{comment}");
            for line in text.lines() {
                let line = line.trim();
                outln!(out, "{comment} {line}");
            }
        }

        if !outer {
            outln!(out);
        }
    }

    Ok(())
}

/// Emit the raw interface for this interface.
fn emit_iface_impl(
    mut out: &mut Writer,
    name: &str,
    version: &str,
    elem: &Node<'_, '_>,
) -> Result<()> {
    outln!(
        out,
        "pub(crate) static INTERFACE: crate::Interface = crate::Interface::new("
    );
    out.indent();

    // Name.
    outln!(out, "cstr!(\"{name}\"),");

    // Version.
    outln!(out, "{version},");
    outln!(out, "REQUESTS,");
    outln!(out, "EVENTS,");
    out.dedent();
    outln!(out, ");");
    outln!(out);

    // Requests.
    outln!(out, "static REQUESTS: &[crate::Message] = &[");
    out.indent();
    for request in elem.children().filter(|n| n.tag_name().name() == "request") {
        emit_message(out, &request)?;
    }
    out.dedent();
    outln!(out, "];");
    outln!(out);

    // Events.
    outln!(out, "static EVENTS: &[crate::Message] = &[");
    out.indent();
    for event in elem.children().filter(|n| n.tag_name().name() == "event") {
        emit_message(out, &event)?;
    }
    out.dedent();
    outln!(out, "];");
    outln!(out);

    Ok(())
}

/// Emit message implementation.
fn emit_message(mut out: &mut Writer, request: &Node<'_, '_>) -> Result<()> {
    let name = request.attribute("name").context("no 'name' attribute")?;
    outln!(out, "crate::Message::new(");
    out.indent();
    outln!(out, "cstr!(\"{name}\"),");

    // Emit the signature.
    out!(out, "sig!(\"");
    for arg in arg_iter(request) {
        match arg.ty {
            Ty::Int => out!(out, "i"),
            Ty::UInt => out!(out, "u"),
            Ty::Fixed => out!(out, "f"),
            Ty::String => {
                if arg.allow_null {
                    out!(out, "?");
                }
                out!(out, "s");
            }
            Ty::Object => {
                if arg.allow_null {
                    out!(out, "?");
                }
                out!(out, "o");
            }
            Ty::NewId => out!(out, "n"),
            Ty::Array => out!(out, "a"),
            Ty::FileDescriptor => out!(out, "h"),
        }
    }
    outln!(out, "\"),");

    outln!(out, "{{");
    out.indent();
    out!(
        out,
        "static TYPES: &[Option<&'static crate::Interface>] = &["
    );
    // Emit foreach interface.
    for arg in arg_iter(request) {
        match arg.ty {
            Ty::Object | Ty::NewId => {
                if let Some(interface) = arg.interface {
                    out!(out, "Some(&super::{}::INTERFACE), ", interface);
                } else {
                    out!(out, "Some(&crate::ANONYMOUS_INTERFACE), ");
                }
            }
            _ => out!(out, "None, "),
        }
    }
    outln!(out, "];");
    outln!(out, "TYPES");
    out.dedent();
    outln!(out, "}}");

    out.dedent();
    outln!(out, "),");

    Ok(())
}

/// Iterate over the arguments for a request or event.
fn arg_iter<'inp>(elem: &'inp Node<'_, '_>) -> impl Iterator<Item = Argument<'inp>> + 'inp {
    elem.children()
        .filter(|n| n.tag_name().name() == "arg")
        .map(|arg| Argument {
            node: arg,
            name: arg.attribute("name").unwrap_or(""),
            ty: match arg.attribute("type").unwrap_or("") {
                "int" => Ty::Int,
                "uint" => Ty::UInt,
                "fixed" => Ty::Fixed,
                "string" => Ty::String,
                "object" => Ty::Object,
                "new_id" => Ty::NewId,
                "array" => Ty::Array,
                "fd" => Ty::FileDescriptor,
                _ => unreachable!(),
            },
            allow_null: arg.attribute("allow-null").unwrap_or("false") == "true",
            interface: arg.attribute("interface").map(|i| i.trim()),
            enum_name: arg.attribute("enum").map(|i| i.trim()),
        })
}

/// Argument for a request or an event.
struct Argument<'a> {
    /// Underlying node.
    node: Node<'a, 'a>,

    /// Name of the argument.
    name: &'a str,

    /// Type of the argument.
    ty: Ty,

    /// Is null allowed?
    allow_null: bool,

    /// Interface to use.
    interface: Option<&'a str>,

    /// Enum for this argument.
    enum_name: Option<&'a str>,
}

/// Type of the argument.
enum Ty {
    /// 32-bit signed integer.
    Int,

    /// 32-bit unsigned integer.
    UInt,

    /// Fixed point number.
    Fixed,

    /// String.
    String,

    /// Object.
    Object,

    /// New ID.
    NewId,

    /// Array.
    Array,

    /// File descriptor.
    FileDescriptor,
}

/// Write out to a file.
#[derive(Default)]
struct Writer {
    /// Buffer to write into.
    buffer: String,

    /// Indent to use.
    indent: u16,
}

impl Writer {
    /// Increment the indent.
    fn indent(&mut self) {
        self.indent += 1;
    }

    /// Decrement the indent.
    fn dedent(&mut self) {
        self.indent -= 1;
    }

    /// Run the provided function with the indent increased.
    fn with_indent(&mut self, f: impl FnOnce(&mut Self)) {
        self.indent();
        f(self);
        self.dedent();
    }
}

impl std::fmt::Write for Writer {
    #[inline]
    fn write_str(&mut self, s: &str) -> std::fmt::Result {
        for (i, line) in s.split('\n').enumerate() {
            if i != 0 {
                self.buffer.push('\n');
            }
            if !line.is_empty() {
                // Add indent if we are beginning a new line.
                if self.buffer.ends_with('\n') {
                    for _ in 0..self.indent {
                        self.buffer.push_str("    ");
                    }
                }

                self.buffer.push_str(line);
            }
        }

        Ok(())
    }
}

/// Sanitize Rust name.
fn sanitize(name: &str) -> &str {
    match name {
        "move" => "move_",
        "90" => "NINETY",
        "180" => "ONE_HUNDRED_EIGHTY",
        "270" => "TWO_HUNDRED_SEVENTY",
        name => name,
    }
}

/// Convert an interface name to an actual path.
struct InterfaceName<'a>(&'a str);

impl fmt::Display for InterfaceName<'_> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "super::{}::{}", self.0, AsUpperCamelCase(self.0))
    }
}

/// Convert an enum name to an actual path.
struct EnumName<'a>(&'a str);

impl fmt::Display for EnumName<'_> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        if let Some((idx, name)) = self.0.split_once('.') {
            write!(f, "super::{}::{}", idx, AsUpperCamelCase(name))
        } else {
            write!(f, "{}", AsUpperCamelCase(self.0))
        }
    }
}
