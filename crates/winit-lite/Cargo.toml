[package]
name = "winit-lite"
version = "0.1.0"
edition = "2024"

[target.'cfg(windows)'.dependencies]
porcupine = { path = "../porcupine" }

[target.'cfg(not(any(windows, target_os = "macos")))'.dependencies]
landway = { path = "../landway", features = ["xdg_shell"] }

[target.'cfg(not(any(windows, target_os = "macos")))'.dependencies.rustix]
version = "1.0.7"
default-features = false
features = ["event", "fs", "pipe", "std"]
