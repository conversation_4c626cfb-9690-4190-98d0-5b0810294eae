// MIT/Apache2 License

use std::io;

use winit_lite::{Display, Event, Poller, WindowAttributes};

fn main() -> io::Result<()> {
    let mut poller = Poller::new()?;
    let mut window = None;

    poller.run(move |display: &mut Display<'_>, event| match event {
        Event::Wakeup(cause) => {
            window = Some(display.create_window(WindowAttributes::default()).unwrap());
            display.exit();
        }
        _ => {}
    })
}
