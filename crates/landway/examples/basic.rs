// MIT/Apache2 License

use landway::Display;

use std::io;

fn main() -> io::Result<()> {
    // Open the display.
    let display = Display::connect()?;
    let mut queue = display.create_queue(None)?;

    display.flush()?;
    display.read(&mut queue)?;
    let read = display.read(&mut queue)?;
    assert_eq!(read.read()?, 0);

    // Get the registry.
    let mut display_proxy = display.display_proxy();
    let _registry = display_proxy.get_registry(&queue)?;

    Ok(())
}
