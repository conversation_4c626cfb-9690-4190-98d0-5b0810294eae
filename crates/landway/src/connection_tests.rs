//! Connection tests for the landway crate.
//!
//! These tests focus on testing Wayland connection environment variable handling.
//! Uses serial_test to avoid environment variable conflicts.

use crate::*;
use serial_test::serial;
use std::env;

/// Test helper to clean up all Wayland-related environment variables
fn cleanup_wayland_env() {
    unsafe {
        env::remove_var("WAYLAND_DISPLAY");
        env::remove_var("WAYLAND_SOCKET");
        env::remove_var("XDG_RUNTIME_DIR");
    }
}

#[test]
#[serial]
fn test_connect_missing_xdg_runtime_dir() {
    cleanup_wayland_env();

    // Set WAYLAND_DISPLAY but not XDG_RUNTIME_DIR
    unsafe {
        env::set_var("WAYLAND_DISPLAY", "wayland-test");
    }

    let result = Display::connect();
    assert!(result.is_err(), "Should fail without XDG_RUNTIME_DIR");

    // Check that the error is about missing XDG_RUNTIME_DIR
    if let Err(error) = result {
        let error_msg = error.to_string();
        assert!(error_msg.contains("XDG_RUNTIME_DIR"),
                "Error should mention XDG_RUNTIME_DIR: {}", error_msg);
    }

    cleanup_wayland_env();
}

#[test]
#[serial]
fn test_connect_invalid_wayland_socket() {
    cleanup_wayland_env();

    // Test with invalid file descriptor string
    unsafe {
        env::set_var("WAYLAND_SOCKET", "not_a_number");
    }

    let result = Display::connect();
    assert!(result.is_err(), "Should fail with invalid WAYLAND_SOCKET");

    cleanup_wayland_env();
}

#[test]
#[serial]
fn test_connect_nonexistent_socket() {
    cleanup_wayland_env();

    // Set up environment for non-existent socket
    unsafe {
        env::set_var("XDG_RUNTIME_DIR", "/tmp");
        env::set_var("WAYLAND_DISPLAY", "nonexistent-socket");
    }

    let result = Display::connect();
    assert!(result.is_err(), "Should fail to connect to nonexistent socket");

    cleanup_wayland_env();
}

#[test]
#[serial]
fn test_environment_variable_edge_cases() {
    cleanup_wayland_env();

    // Test with empty WAYLAND_DISPLAY
    unsafe {
        env::set_var("XDG_RUNTIME_DIR", "/tmp");
        env::set_var("WAYLAND_DISPLAY", "");
    }

    let result = Display::connect();
    assert!(result.is_err(), "Should fail with empty WAYLAND_DISPLAY");

    cleanup_wayland_env();
}



#[test]
#[serial]
fn test_display_from_ptr_non_owned() {
    cleanup_wayland_env();

    // Test creating display from a fake pointer without ownership
    let fake_ptr = 0x1000 as *mut ();
    let result = unsafe { Display::from_ptr(fake_ptr, false) };
    assert!(result.is_ok(), "Should handle fake pointer gracefully");

    cleanup_wayland_env();
}



#[test]
#[serial]
fn test_default_wayland_display() {
    cleanup_wayland_env();

    // Test that WAYLAND_DISPLAY defaults to "wayland-0" when not set
    unsafe {
        env::set_var("XDG_RUNTIME_DIR", "/tmp");
        // Don't set WAYLAND_DISPLAY, should default to "wayland-0"
    }

    let result = Display::connect();
    assert!(result.is_err(), "Should fail to connect to default socket");

    cleanup_wayland_env();
}

#[test]
#[serial]
fn test_absolute_wayland_display_path() {
    cleanup_wayland_env();

    // Test with absolute path in WAYLAND_DISPLAY
    unsafe {
        env::set_var("WAYLAND_DISPLAY", "/tmp/wayland-absolute-test");
        // XDG_RUNTIME_DIR should be ignored for absolute paths
    }

    let result = Display::connect();
    assert!(result.is_err(), "Should fail to connect to absolute path socket");

    cleanup_wayland_env();
}

#[test]
#[serial]
fn test_library_loading_multiple_times() {
    cleanup_wayland_env();

    // Test that library can be loaded multiple times
    let lib1 = Library::get();
    let lib2 = Library::get();

    assert!(lib1.is_ok(), "First library load should succeed");
    assert!(lib2.is_ok(), "Second library load should succeed");

    cleanup_wayland_env();
}



