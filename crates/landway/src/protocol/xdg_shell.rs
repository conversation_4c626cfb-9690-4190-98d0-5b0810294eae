// MIT/Apache2 License
// This file is automatically generated! Do not edit this file manually.
// Please see the landway-gen code for more info.

//! The `xdg_shell` protocol.

#![allow(
    clippy::doc_lazy_continuation,
    clippy::doc_markdown,
    clippy::match_single_binding,
    clippy::missing_errors_doc,
    clippy::missing_panics_doc,
    clippy::unreadable_literal,
    clippy::redundant_closure_for_method_calls,
    clippy::too_many_lines,
    unused_imports,
    unused_mut
)]

#[allow(clippy::wildcard_imports)]
use super::__all::*;

// Original Wayland copyright information
// -------------------------------------------------
// 
// Copyright © 2008-2013 <PERSON><PERSON>
// Copyright © 2013      <PERSON>
// Copyright © 2013      Jasper St. Pierre
// Copyright © 2010-2013 Intel Corporation
// Copyright © 2015-2017 Samsung Electronics Co., Ltd
// Copyright © 2015-2017 Red Hat Inc.
// 
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the "Software"),
// to deal in the Software without restriction, including without limitation
// the rights to use, copy, modify, merge, publish, distribute, sublicense,
// and/or sell copies of the Software, and to permit persons to whom the
// Software is furnished to do so, subject to the following conditions:
// 
// The above copyright notice and this permission notice (including the next
// paragraph) shall be included in all copies or substantial portions of the
// Software.
// 
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
// DEALINGS IN THE SOFTWARE.
// 
// -------------------------------------------------

pub mod xdg_wm_base {
    //!  xdg_wm_base: create desktop-style surfaces
    //! 
    //!  
    //!  The xdg_wm_base interface is exposed as a global object enabling clients
    //!  to turn their wl_surfaces into windows in a desktop environment. It
    //!  defines the basic functionality needed for clients and the compositor to
    //!  create windows that can be dragged, resized, maximized, etc, as well as
    //!  creating transient windows such as popup menus.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `xdg_wm_base` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct XdgWmBase(Proxy);

    impl fmt::Debug for XdgWmBase {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("XdgWmBase").field(&self.0).finish()
        }
    }

    impl From<Proxy> for XdgWmBase {
        fn from(proxy: Proxy) -> Self {
            XdgWmBase(proxy)
        }
    }

    impl From<XdgWmBase> for Proxy {
        fn from(proxy: XdgWmBase) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for XdgWmBase {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for XdgWmBase {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl XdgWmBase {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl XdgWmBase {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroy xdg_wm_base
            //! 
            //!  
            //!  Destroy this xdg_wm_base object.
            //!  
            //!  Destroying a bound xdg_wm_base object while there are surfaces
            //!  still alive created by this xdg_wm_base object instance is illegal
            //!  and will result in a defunct_surfaces error.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn create_positioner(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::xdg_positioner::XdgPositioner> {
            //!  create_positioner: create a positioner object
            //! 
            //!  
            //!  Create a positioner object. A positioner object is used to position
            //!  surfaces relative to some parent surface. See the interface description
            //!  and xdg_surface.get_popup for details.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::xdg_positioner::XdgPositioner::INTERFACE,
                super::xdg_positioner::XdgPositioner::VERSION,
                event_queue
            )?;
            Ok(super::xdg_positioner::XdgPositioner::from(proxy))
        }

        pub fn get_xdg_surface(
            &mut self,
            event_queue: &crate::EventQueue,
            surface: &super::wl_surface::WlSurface, 
        ) -> io::Result<super::xdg_surface::XdgSurface> {
            //!  get_xdg_surface: create a shell surface from a surface
            //! 
            //!  
            //!  This creates an xdg_surface for the given surface. While xdg_surface
            //!  itself is not a role, the corresponding surface may only be assigned
            //!  a role extending xdg_surface, such as xdg_toplevel or xdg_popup. It is
            //!  illegal to create an xdg_surface for a wl_surface which already has an
            //!  assigned role and this will result in a role error.
            //!  
            //!  This creates an xdg_surface for the given surface. An xdg_surface is
            //!  used as basis to define a role to a given surface, such as xdg_toplevel
            //!  or xdg_popup. It also manages functionality shared between xdg_surface
            //!  based surface roles.
            //!  
            //!  See the documentation of xdg_surface for more details about what an
            //!  xdg_surface is and how it is used.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (NewId, 0),
                (Object, Some(surface.as_ref())),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::xdg_surface::XdgSurface::INTERFACE,
                super::xdg_surface::XdgSurface::VERSION,
                event_queue
            )?;
            Ok(super::xdg_surface::XdgSurface::from(proxy))
        }

        pub fn pong(
            &mut self,
            serial: u32, 
        ) -> io::Result<()> {
            //!  pong: respond to a ping event
            //! 
            //!  
            //!  A client must respond to a ping event with a pong request or
            //!  the client may be deemed unresponsive. See xdg_wm_base.ping
            //!  and xdg_wm_base.error.unresponsive.
            //!  

            const OPCODE: u32 = 3;

            crate::args!(args = 
                (UInt32, serial),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Ping {
                            serial,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 7;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  ping: check if the client is alive
        /// 
        ///  
        ///  The ping event asks the client if it's still alive. Pass the
        ///  serial specified in the event back to the compositor by sending
        ///  a "pong" request back with the specified serial. See xdg_wm_base.pong.
        ///  
        ///  Compositors can use this to determine if the client is still
        ///  alive. It's unspecified what will happen if the client doesn't
        ///  respond to the ping request, or in what timeframe. Clients should
        ///  try to respond in a reasonable amount of time. The “unresponsive”
        ///  error is provided for compositors that wish to disconnect unresponsive
        ///  clients.
        ///  
        ///  A compositor is free to ping in any way it wants, but a client must
        ///  always respond to any xdg_wm_base object it created.
        ///  
        Ping {
            /// serial - pass this to the pong request
            serial: u32,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("xdg_wm_base"),
        7,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("create_positioner"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::xdg_positioner::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("get_xdg_surface"),
            sig!("no"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::xdg_surface::INTERFACE), Some(&super::wl_surface::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("pong"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("ping"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// role - given wl_surface has another role
        pub const ROLE: Error = Error(0);

        /// defunct_surfaces - xdg_wm_base was destroyed before children
        pub const DEFUNCT_SURFACES: Error = Error(1);

        /// not_the_topmost_popup - the client tried to map or destroy a non-topmost popup
        pub const NOT_THE_TOPMOST_POPUP: Error = Error(2);

        /// invalid_popup_parent - the client specified an invalid popup parent surface
        pub const INVALID_POPUP_PARENT: Error = Error(3);

        /// invalid_surface_state - the client provided an invalid surface state
        pub const INVALID_SURFACE_STATE: Error = Error(4);

        /// invalid_positioner - the client provided an invalid positioner
        pub const INVALID_POSITIONER: Error = Error(5);

        /// unresponsive - the client didn’t respond to a ping event in time
        pub const UNRESPONSIVE: Error = Error(6);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::xdg_wm_base::XdgWmBase;

pub mod xdg_positioner {
    //!  xdg_positioner: child surface positioner
    //! 
    //!  
    //!  The xdg_positioner provides a collection of rules for the placement of a
    //!  child surface relative to a parent surface. Rules can be defined to ensure
    //!  the child surface remains within the visible area's borders, and to
    //!  specify how the child surface changes its position, such as sliding along
    //!  an axis, or flipping around a rectangle. These positioner-created rules are
    //!  constrained by the requirement that a child surface must intersect with or
    //!  be at least partially adjacent to its parent surface.
    //!  
    //!  See the various requests for details about possible rules.
    //!  
    //!  At the time of the request, the compositor makes a copy of the rules
    //!  specified by the xdg_positioner. Thus, after the request is complete the
    //!  xdg_positioner object can be destroyed or reused; further changes to the
    //!  object will have no effect on previous usages.
    //!  
    //!  For an xdg_positioner object to be considered complete, it must have a
    //!  non-zero size set by set_size, and a non-zero anchor rectangle set by
    //!  set_anchor_rect. Passing an incomplete xdg_positioner object when
    //!  positioning a surface raises an invalid_positioner error.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `xdg_positioner` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct XdgPositioner(Proxy);

    impl fmt::Debug for XdgPositioner {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("XdgPositioner").field(&self.0).finish()
        }
    }

    impl From<Proxy> for XdgPositioner {
        fn from(proxy: Proxy) -> Self {
            XdgPositioner(proxy)
        }
    }

    impl From<XdgPositioner> for Proxy {
        fn from(proxy: XdgPositioner) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for XdgPositioner {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for XdgPositioner {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl XdgPositioner {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl XdgPositioner {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroy the xdg_positioner object
            //! 
            //!  
            //!  Notify the compositor that the xdg_positioner will no longer be used.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_size(
            &mut self,
            width: i32,
            height: i32,
        ) -> io::Result<()> {
            //!  set_size: set the size of the to-be positioned rectangle
            //! 
            //!  
            //!  Set the size of the surface that is to be positioned with the positioner
            //!  object. The size is in surface-local coordinates and corresponds to the
            //!  window geometry. See xdg_surface.set_window_geometry.
            //!  
            //!  If a zero or negative size is set the invalid_input error is raised.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (Int32, width),
                (Int32, height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_anchor_rect(
            &mut self,
            x: i32,
            y: i32,
            width: i32,
            height: i32,
        ) -> io::Result<()> {
            //!  set_anchor_rect: set the anchor rectangle within the parent surface
            //! 
            //!  
            //!  Specify the anchor rectangle within the parent surface that the child
            //!  surface will be placed relative to. The rectangle is relative to the
            //!  window geometry as defined by xdg_surface.set_window_geometry of the
            //!  parent surface.
            //!  
            //!  When the xdg_positioner object is used to position a child surface, the
            //!  anchor rectangle may not extend outside the window geometry of the
            //!  positioned child's parent surface.
            //!  
            //!  If a negative size is set the invalid_input error is raised.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (Int32, x),
                (Int32, y),
                (Int32, width),
                (Int32, height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_anchor(
            &mut self,
            anchor: Anchor,
        ) -> io::Result<()> {
            //!  set_anchor: set anchor rectangle anchor
            //! 
            //!  
            //!  Defines the anchor point for the anchor rectangle. The specified anchor
            //!  is used derive an anchor point that the child surface will be
            //!  positioned relative to. If a corner anchor is set (e.g. 'top_left' or
            //!  'bottom_right'), the anchor point will be at the specified corner;
            //!  otherwise, the derived anchor point will be centered on the specified
            //!  edge, or in the center of the anchor rectangle if no edge is specified.
            //!  

            const OPCODE: u32 = 3;

            let anchor = anchor.into();
            crate::args!(args = 
                (UInt32, anchor),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_gravity(
            &mut self,
            gravity: Gravity,
        ) -> io::Result<()> {
            //!  set_gravity: set child surface gravity
            //! 
            //!  
            //!  Defines in what direction a surface should be positioned, relative to
            //!  the anchor point of the parent surface. If a corner gravity is
            //!  specified (e.g. 'bottom_right' or 'top_left'), then the child surface
            //!  will be placed towards the specified gravity; otherwise, the child
            //!  surface will be centered over the anchor point on any axis that had no
            //!  gravity specified. If the gravity is not in the ‘gravity’ enum, an
            //!  invalid_input error is raised.
            //!  

            const OPCODE: u32 = 4;

            let gravity = gravity.into();
            crate::args!(args = 
                (UInt32, gravity),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_constraint_adjustment(
            &mut self,
            constraint_adjustment: ConstraintAdjustment,
        ) -> io::Result<()> {
            //!  set_constraint_adjustment: set the adjustment to be done when constrained
            //! 
            //!  
            //!  Specify how the window should be positioned if the originally intended
            //!  position caused the surface to be constrained, meaning at least
            //!  partially outside positioning boundaries set by the compositor. The
            //!  adjustment is set by constructing a bitmask describing the adjustment to
            //!  be made when the surface is constrained on that axis.
            //!  
            //!  If no bit for one axis is set, the compositor will assume that the child
            //!  surface should not change its position on that axis when constrained.
            //!  
            //!  If more than one bit for one axis is set, the order of how adjustments
            //!  are applied is specified in the corresponding adjustment descriptions.
            //!  
            //!  The default adjustment is none.
            //!  

            const OPCODE: u32 = 5;

            let constraint_adjustment = constraint_adjustment.into();
            crate::args!(args = 
                (UInt32, constraint_adjustment),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_offset(
            &mut self,
            x: i32,
            y: i32,
        ) -> io::Result<()> {
            //!  set_offset: set surface position offset
            //! 
            //!  
            //!  Specify the surface position offset relative to the position of the
            //!  anchor on the anchor rectangle and the anchor on the surface. For
            //!  example if the anchor of the anchor rectangle is at (x, y), the surface
            //!  has the gravity bottom|right, and the offset is (ox, oy), the calculated
            //!  surface position will be (x + ox, y + oy). The offset position of the
            //!  surface is the one used for constraint testing. See
            //!  set_constraint_adjustment.
            //!  
            //!  An example use case is placing a popup menu on top of a user interface
            //!  element, while aligning the user interface element of the parent surface
            //!  with some user interface element placed somewhere in the popup surface.
            //!  

            const OPCODE: u32 = 6;

            crate::args!(args = 
                (Int32, x),
                (Int32, y),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_reactive(
            &mut self,
        ) -> io::Result<()> {
            //!  set_reactive: continuously reconstrain the surface
            //! 
            //!  
            //!  When set reactive, the surface is reconstrained if the conditions used
            //!  for constraining changed, e.g. the parent window moved.
            //!  
            //!  If the conditions changed and the popup was reconstrained, an
            //!  xdg_popup.configure event is sent with updated geometry, followed by an
            //!  xdg_surface.configure event.
            //!  

            const OPCODE: u32 = 7;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_parent_size(
            &mut self,
            parent_width: i32,
            parent_height: i32,
        ) -> io::Result<()> {
            //!  set_parent_size: 
            //! 
            //!  
            //!  Set the parent window geometry the compositor should use when
            //!  positioning the popup. The compositor may use this information to
            //!  determine the future state the popup should be constrained using. If
            //!  this doesn't match the dimension of the parent the popup is eventually
            //!  positioned against, the behavior is undefined.
            //!  
            //!  The arguments are given in the surface-local coordinate space.
            //!  

            const OPCODE: u32 = 8;

            crate::args!(args = 
                (Int32, parent_width),
                (Int32, parent_height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_parent_configure(
            &mut self,
            serial: u32, 
        ) -> io::Result<()> {
            //!  set_parent_configure: set parent configure this is a response to
            //! 
            //!  
            //!  Set the serial of an xdg_surface.configure event this positioner will be
            //!  used in response to. The compositor may use this information together
            //!  with set_parent_size to determine what future state the popup should be
            //!  constrained using.
            //!  

            const OPCODE: u32 = 9;

            crate::args!(args = 
                (UInt32, serial),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 7;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("xdg_positioner"),
        7,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_size"),
            sig!("ii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_anchor_rect"),
            sig!("iiii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_anchor"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_gravity"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_constraint_adjustment"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_offset"),
            sig!("ii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_reactive"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_parent_size"),
            sig!("ii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_parent_configure"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// invalid_input - invalid input provided
        pub const INVALID_INPUT: Error = Error(0);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Anchor(u32);

    impl Anchor {
        /// none - 
        pub const NONE: Anchor = Anchor(0);

        /// top - 
        pub const TOP: Anchor = Anchor(1);

        /// bottom - 
        pub const BOTTOM: Anchor = Anchor(2);

        /// left - 
        pub const LEFT: Anchor = Anchor(3);

        /// right - 
        pub const RIGHT: Anchor = Anchor(4);

        /// top_left - 
        pub const TOP_LEFT: Anchor = Anchor(5);

        /// bottom_left - 
        pub const BOTTOM_LEFT: Anchor = Anchor(6);

        /// top_right - 
        pub const TOP_RIGHT: Anchor = Anchor(7);

        /// bottom_right - 
        pub const BOTTOM_RIGHT: Anchor = Anchor(8);

    }

    impl From<u32> for Anchor {
        fn from(value: u32) -> Self {
            Anchor(value)
        }
    }

    impl From<i32> for Anchor {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Anchor::from(value as u32)
        }
    }

    impl From<Anchor> for u32 {
        fn from(value: Anchor) -> Self {
            value.0
        }
    }

    impl From<Anchor> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Anchor) -> Self {
            value.0 as i32
        }
    }

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Gravity(u32);

    impl Gravity {
        /// none - 
        pub const NONE: Gravity = Gravity(0);

        /// top - 
        pub const TOP: Gravity = Gravity(1);

        /// bottom - 
        pub const BOTTOM: Gravity = Gravity(2);

        /// left - 
        pub const LEFT: Gravity = Gravity(3);

        /// right - 
        pub const RIGHT: Gravity = Gravity(4);

        /// top_left - 
        pub const TOP_LEFT: Gravity = Gravity(5);

        /// bottom_left - 
        pub const BOTTOM_LEFT: Gravity = Gravity(6);

        /// top_right - 
        pub const TOP_RIGHT: Gravity = Gravity(7);

        /// bottom_right - 
        pub const BOTTOM_RIGHT: Gravity = Gravity(8);

    }

    impl From<u32> for Gravity {
        fn from(value: u32) -> Self {
            Gravity(value)
        }
    }

    impl From<i32> for Gravity {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Gravity::from(value as u32)
        }
    }

    impl From<Gravity> for u32 {
        fn from(value: Gravity) -> Self {
            value.0
        }
    }

    impl From<Gravity> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Gravity) -> Self {
            value.0 as i32
        }
    }

    ///  constraint_adjustment: constraint adjustments
    /// 
    ///  
    ///  The constraint adjustment value define ways the compositor will adjust
    ///  the position of the surface, if the unadjusted position would result
    ///  in the surface being partly constrained.
    ///  
    ///  Whether a surface is considered 'constrained' is left to the compositor
    ///  to determine. For example, the surface may be partly outside the
    ///  compositor's defined 'work area', thus necessitating the child surface's
    ///  position be adjusted until it is entirely inside the work area.
    ///  
    ///  The adjustments can be combined, according to a defined precedence: 1)
    ///  Flip, 2) Slide, 3) Resize.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct ConstraintAdjustment(u32);

    impl ConstraintAdjustment {
        /// none - 
        pub const NONE: ConstraintAdjustment = ConstraintAdjustment(0);

        /// slide_x - 
        pub const SLIDE_X: ConstraintAdjustment = ConstraintAdjustment(1);

        /// slide_y - 
        pub const SLIDE_Y: ConstraintAdjustment = ConstraintAdjustment(2);

        /// flip_x - 
        pub const FLIP_X: ConstraintAdjustment = ConstraintAdjustment(4);

        /// flip_y - 
        pub const FLIP_Y: ConstraintAdjustment = ConstraintAdjustment(8);

        /// resize_x - 
        pub const RESIZE_X: ConstraintAdjustment = ConstraintAdjustment(16);

        /// resize_y - 
        pub const RESIZE_Y: ConstraintAdjustment = ConstraintAdjustment(32);

    }

    impl From<u32> for ConstraintAdjustment {
        fn from(value: u32) -> Self {
            ConstraintAdjustment(value)
        }
    }

    impl From<i32> for ConstraintAdjustment {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            ConstraintAdjustment::from(value as u32)
        }
    }

    impl From<ConstraintAdjustment> for u32 {
        fn from(value: ConstraintAdjustment) -> Self {
            value.0
        }
    }

    impl From<ConstraintAdjustment> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: ConstraintAdjustment) -> Self {
            value.0 as i32
        }
    }

}

pub use self::xdg_positioner::XdgPositioner;

pub mod xdg_surface {
    //!  xdg_surface: desktop user interface surface base interface
    //! 
    //!  
    //!  An interface that may be implemented by a wl_surface, for
    //!  implementations that provide a desktop-style user interface.
    //!  
    //!  It provides a base set of functionality required to construct user
    //!  interface elements requiring management by the compositor, such as
    //!  toplevel windows, menus, etc. The types of functionality are split into
    //!  xdg_surface roles.
    //!  
    //!  Creating an xdg_surface does not set the role for a wl_surface. In order
    //!  to map an xdg_surface, the client must create a role-specific object
    //!  using, e.g., get_toplevel, get_popup. The wl_surface for any given
    //!  xdg_surface can have at most one role, and may not be assigned any role
    //!  not based on xdg_surface.
    //!  
    //!  A role must be assigned before any other requests are made to the
    //!  xdg_surface object.
    //!  
    //!  The client must call wl_surface.commit on the corresponding wl_surface
    //!  for the xdg_surface state to take effect.
    //!  
    //!  Creating an xdg_surface from a wl_surface which has a buffer attached or
    //!  committed is a client error, and any attempts by a client to attach or
    //!  manipulate a buffer prior to the first xdg_surface.configure call must
    //!  also be treated as errors.
    //!  
    //!  After creating a role-specific object and setting it up (e.g. by sending
    //!  the title, app ID, size constraints, parent, etc), the client must
    //!  perform an initial commit without any buffer attached. The compositor
    //!  will reply with initial wl_surface state such as
    //!  wl_surface.preferred_buffer_scale followed by an xdg_surface.configure
    //!  event. The client must acknowledge it and is then allowed to attach a
    //!  buffer to map the surface.
    //!  
    //!  Mapping an xdg_surface-based role surface is defined as making it
    //!  possible for the surface to be shown by the compositor. Note that
    //!  a mapped surface is not guaranteed to be visible once it is mapped.
    //!  
    //!  For an xdg_surface to be mapped by the compositor, the following
    //!  conditions must be met:
    //!  (1) the client has assigned an xdg_surface-based role to the surface
    //!  (2) the client has set and committed the xdg_surface state and the
    //!  role-dependent state to the surface
    //!  (3) the client has committed a buffer to the surface
    //!  
    //!  A newly-unmapped surface is considered to have met condition (1) out
    //!  of the 3 required conditions for mapping a surface if its role surface
    //!  has not been destroyed, i.e. the client must perform the initial commit
    //!  again before attaching a buffer.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `xdg_surface` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct XdgSurface(Proxy);

    impl fmt::Debug for XdgSurface {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("XdgSurface").field(&self.0).finish()
        }
    }

    impl From<Proxy> for XdgSurface {
        fn from(proxy: Proxy) -> Self {
            XdgSurface(proxy)
        }
    }

    impl From<XdgSurface> for Proxy {
        fn from(proxy: XdgSurface) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for XdgSurface {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for XdgSurface {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl XdgSurface {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl XdgSurface {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroy the xdg_surface
            //! 
            //!  
            //!  Destroy the xdg_surface object. An xdg_surface must only be destroyed
            //!  after its role object has been destroyed, otherwise
            //!  a defunct_role_object error is raised.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn get_toplevel(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::xdg_toplevel::XdgToplevel> {
            //!  get_toplevel: assign the xdg_toplevel surface role
            //! 
            //!  
            //!  This creates an xdg_toplevel object for the given xdg_surface and gives
            //!  the associated wl_surface the xdg_toplevel role.
            //!  
            //!  See the documentation of xdg_toplevel for more details about what an
            //!  xdg_toplevel is and how it is used.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::xdg_toplevel::XdgToplevel::INTERFACE,
                super::xdg_toplevel::XdgToplevel::VERSION,
                event_queue
            )?;
            Ok(super::xdg_toplevel::XdgToplevel::from(proxy))
        }

        pub fn get_popup(
            &mut self,
            event_queue: &crate::EventQueue,
            parent: Option<&super::xdg_surface::XdgSurface>, 
            positioner: &super::xdg_positioner::XdgPositioner, 
        ) -> io::Result<super::xdg_popup::XdgPopup> {
            //!  get_popup: assign the xdg_popup surface role
            //! 
            //!  
            //!  This creates an xdg_popup object for the given xdg_surface and gives
            //!  the associated wl_surface the xdg_popup role.
            //!  
            //!  If null is passed as a parent, a parent surface must be specified using
            //!  some other protocol, before committing the initial state.
            //!  
            //!  See the documentation of xdg_popup for more details about what an
            //!  xdg_popup is and how it is used.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (NewId, 0),
                (NObject, parent.map(|x| x.as_proxy())),
                (Object, Some(positioner.as_ref())),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::xdg_popup::XdgPopup::INTERFACE,
                super::xdg_popup::XdgPopup::VERSION,
                event_queue
            )?;
            Ok(super::xdg_popup::XdgPopup::from(proxy))
        }

        pub fn set_window_geometry(
            &mut self,
            x: i32,
            y: i32,
            width: i32,
            height: i32,
        ) -> io::Result<()> {
            //!  set_window_geometry: set the new window geometry
            //! 
            //!  
            //!  The window geometry of a surface is its "visible bounds" from the
            //!  user's perspective. Client-side decorations often have invisible
            //!  portions like drop-shadows which should be ignored for the
            //!  purposes of aligning, placing and constraining windows.
            //!  
            //!  The window geometry is double-buffered state, see wl_surface.commit.
            //!  
            //!  When maintaining a position, the compositor should treat the (x, y)
            //!  coordinate of the window geometry as the top left corner of the window.
            //!  A client changing the (x, y) window geometry coordinate should in
            //!  general not alter the position of the window.
            //!  
            //!  Once the window geometry of the surface is set, it is not possible to
            //!  unset it, and it will remain the same until set_window_geometry is
            //!  called again, even if a new subsurface or buffer is attached.
            //!  
            //!  If never set, the value is the full bounds of the surface,
            //!  including any subsurfaces. This updates dynamically on every
            //!  commit. This unset is meant for extremely simple clients.
            //!  
            //!  The arguments are given in the surface-local coordinate space of
            //!  the wl_surface associated with this xdg_surface, and may extend outside
            //!  of the wl_surface itself to mark parts of the subsurface tree as part of
            //!  the window geometry.
            //!  
            //!  When applied, the effective window geometry will be the set window
            //!  geometry clamped to the bounding rectangle of the combined
            //!  geometry of the surface of the xdg_surface and the associated
            //!  subsurfaces.
            //!  
            //!  The effective geometry will not be recalculated unless a new call to
            //!  set_window_geometry is done and the new pending surface state is
            //!  subsequently applied.
            //!  
            //!  The width and height of the effective window geometry must be
            //!  greater than zero. Setting an invalid size will raise an
            //!  invalid_size error.
            //!  

            const OPCODE: u32 = 3;

            crate::args!(args = 
                (Int32, x),
                (Int32, y),
                (Int32, width),
                (Int32, height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn ack_configure(
            &mut self,
            serial: u32, 
        ) -> io::Result<()> {
            //!  ack_configure: ack a configure event
            //! 
            //!  
            //!  When a configure event is received, if a client commits the
            //!  surface in response to the configure event, then the client
            //!  must make an ack_configure request sometime before the commit
            //!  request, passing along the serial of the configure event.
            //!  
            //!  For instance, for toplevel surfaces the compositor might use this
            //!  information to move a surface to the top left only when the client has
            //!  drawn itself for the maximized or fullscreen state.
            //!  
            //!  If the client receives multiple configure events before it
            //!  can respond to one, it only has to ack the last configure event.
            //!  Acking a configure event that was never sent raises an invalid_serial
            //!  error.
            //!  
            //!  A client is not required to commit immediately after sending
            //!  an ack_configure request - it may even ack_configure several times
            //!  before its next surface commit.
            //!  
            //!  A client may send multiple ack_configure requests before committing, but
            //!  only the last request sent before a commit indicates which configure
            //!  event the client really is responding to.
            //!  
            //!  Sending an ack_configure request consumes the serial number sent with
            //!  the request, as well as serial numbers sent by all configure events
            //!  sent on this xdg_surface prior to the configure event referenced by
            //!  the committed serial.
            //!  
            //!  It is an error to issue multiple ack_configure requests referencing a
            //!  serial from the same configure event, or to issue an ack_configure
            //!  request referencing a serial from a configure event issued before the
            //!  event identified by the last ack_configure request for the same
            //!  xdg_surface. Doing so will raise an invalid_serial error.
            //!  

            const OPCODE: u32 = 4;

            crate::args!(args = 
                (UInt32, serial),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Configure {
                            serial,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 7;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  configure: suggest a surface change
        /// 
        ///  
        ///  The configure event marks the end of a configure sequence. A configure
        ///  sequence is a set of one or more events configuring the state of the
        ///  xdg_surface, including the final xdg_surface.configure event.
        ///  
        ///  Where applicable, xdg_surface surface roles will during a configure
        ///  sequence extend this event as a latched state sent as events before the
        ///  xdg_surface.configure event. Such events should be considered to make up
        ///  a set of atomically applied configuration states, where the
        ///  xdg_surface.configure commits the accumulated state.
        ///  
        ///  Clients should arrange their surface for the new states, and then send
        ///  an ack_configure request with the serial sent in this configure event at
        ///  some point before committing the new surface.
        ///  
        ///  If the client receives multiple configure events before it can respond
        ///  to one, it is free to discard all but the last event it received.
        ///  
        Configure {
            /// serial - serial of the configure event
            serial: u32,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("xdg_surface"),
        7,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("get_toplevel"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::xdg_toplevel::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("get_popup"),
            sig!("n?oo"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::xdg_popup::INTERFACE), Some(&super::xdg_surface::INTERFACE), Some(&super::xdg_positioner::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_window_geometry"),
            sig!("iiii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("ack_configure"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("configure"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// not_constructed - Surface was not fully constructed
        pub const NOT_CONSTRUCTED: Error = Error(1);

        /// already_constructed - Surface was already constructed
        pub const ALREADY_CONSTRUCTED: Error = Error(2);

        /// unconfigured_buffer - Attaching a buffer to an unconfigured surface
        pub const UNCONFIGURED_BUFFER: Error = Error(3);

        /// invalid_serial - Invalid serial number when acking a configure event
        pub const INVALID_SERIAL: Error = Error(4);

        /// invalid_size - Width or height was zero or negative
        pub const INVALID_SIZE: Error = Error(5);

        /// defunct_role_object - Surface was destroyed before its role object
        pub const DEFUNCT_ROLE_OBJECT: Error = Error(6);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::xdg_surface::XdgSurface;

pub mod xdg_toplevel {
    //!  xdg_toplevel: toplevel surface
    //! 
    //!  
    //!  This interface defines an xdg_surface role which allows a surface to,
    //!  among other things, set window-like properties such as maximize,
    //!  fullscreen, and minimize, set application-specific metadata like title and
    //!  id, and well as trigger user interactive operations such as interactive
    //!  resize and move.
    //!  
    //!  A xdg_toplevel by default is responsible for providing the full intended
    //!  visual representation of the toplevel, which depending on the window
    //!  state, may mean things like a title bar, window controls and drop shadow.
    //!  
    //!  Unmapping an xdg_toplevel means that the surface cannot be shown
    //!  by the compositor until it is explicitly mapped again.
    //!  All active operations (e.g., move, resize) are canceled and all
    //!  attributes (e.g. title, state, stacking, ...) are discarded for
    //!  an xdg_toplevel surface when it is unmapped. The xdg_toplevel returns to
    //!  the state it had right after xdg_surface.get_toplevel. The client
    //!  can re-map the toplevel by performing a commit without any buffer
    //!  attached, waiting for a configure event and handling it as usual (see
    //!  xdg_surface description).
    //!  
    //!  Attaching a null buffer to a toplevel unmaps the surface.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `xdg_toplevel` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct XdgToplevel(Proxy);

    impl fmt::Debug for XdgToplevel {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("XdgToplevel").field(&self.0).finish()
        }
    }

    impl From<Proxy> for XdgToplevel {
        fn from(proxy: Proxy) -> Self {
            XdgToplevel(proxy)
        }
    }

    impl From<XdgToplevel> for Proxy {
        fn from(proxy: XdgToplevel) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for XdgToplevel {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for XdgToplevel {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl XdgToplevel {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl XdgToplevel {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroy the xdg_toplevel
            //! 
            //!  
            //!  This request destroys the role surface and unmaps the surface;
            //!  see "Unmapping" behavior in interface section for details.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_parent(
            &mut self,
            parent: Option<&super::xdg_toplevel::XdgToplevel>, 
        ) -> io::Result<()> {
            //!  set_parent: set the parent of this surface
            //! 
            //!  
            //!  Set the "parent" of this surface. This surface should be stacked
            //!  above the parent surface and all other ancestor surfaces.
            //!  
            //!  Parent surfaces should be set on dialogs, toolboxes, or other
            //!  "auxiliary" surfaces, so that the parent is raised when the dialog
            //!  is raised.
            //!  
            //!  Setting a null parent for a child surface unsets its parent. Setting
            //!  a null parent for a surface which currently has no parent is a no-op.
            //!  
            //!  Only mapped surfaces can have child surfaces. Setting a parent which
            //!  is not mapped is equivalent to setting a null parent. If a surface
            //!  becomes unmapped, its children's parent is set to the parent of
            //!  the now-unmapped surface. If the now-unmapped surface has no parent,
            //!  its children's parent is unset. If the now-unmapped surface becomes
            //!  mapped again, its parent-child relationship is not restored.
            //!  
            //!  The parent toplevel must not be one of the child toplevel's
            //!  descendants, and the parent must be different from the child toplevel,
            //!  otherwise the invalid_parent protocol error is raised.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NObject, parent.map(|x| x.as_proxy())),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_title(
            &mut self,
            title: &CStr, 
        ) -> io::Result<()> {
            //!  set_title: set surface title
            //! 
            //!  
            //!  Set a short title for the surface.
            //!  
            //!  This string may be used to identify the surface in a task bar,
            //!  window list, or other user interface elements provided by the
            //!  compositor.
            //!  
            //!  The string must be encoded in UTF-8.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (String, Some(title)),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_app_id(
            &mut self,
            app_id: &CStr, 
        ) -> io::Result<()> {
            //!  set_app_id: set application ID
            //! 
            //!  
            //!  Set an application identifier for the surface.
            //!  
            //!  The app ID identifies the general class of applications to which
            //!  the surface belongs. The compositor can use this to group multiple
            //!  surfaces together, or to determine how to launch a new application.
            //!  
            //!  For D-Bus activatable applications, the app ID is used as the D-Bus
            //!  service name.
            //!  
            //!  The compositor shell will try to group application surfaces together
            //!  by their app ID. As a best practice, it is suggested to select app
            //!  ID's that match the basename of the application's .desktop file.
            //!  For example, "org.freedesktop.FooViewer" where the .desktop file is
            //!  "org.freedesktop.FooViewer.desktop".
            //!  
            //!  Like other properties, a set_app_id request can be sent after the
            //!  xdg_toplevel has been mapped to update the property.
            //!  
            //!  See the desktop-entry specification [0] for more details on
            //!  application identifiers and how they relate to well-known D-Bus
            //!  names and .desktop files.
            //!  
            //!  [0] https://standards.freedesktop.org/desktop-entry-spec/
            //!  

            const OPCODE: u32 = 3;

            crate::args!(args = 
                (String, Some(app_id)),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn show_window_menu(
            &mut self,
            seat: &super::wl_seat::WlSeat, 
            serial: u32, 
            x: i32,
            y: i32,
        ) -> io::Result<()> {
            //!  show_window_menu: show the window menu
            //! 
            //!  
            //!  Clients implementing client-side decorations might want to show
            //!  a context menu when right-clicking on the decorations, giving the
            //!  user a menu that they can use to maximize or minimize the window.
            //!  
            //!  This request asks the compositor to pop up such a window menu at
            //!  the given position, relative to the local surface coordinates of
            //!  the parent surface. There are no guarantees as to what menu items
            //!  the window menu contains, or even if a window menu will be drawn
            //!  at all.
            //!  
            //!  This request must be used in response to some sort of user action
            //!  like a button press, key press, or touch down event.
            //!  

            const OPCODE: u32 = 4;

            crate::args!(args = 
                (Object, Some(seat.as_ref())),
                (UInt32, serial),
                (Int32, x),
                (Int32, y),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn move_(
            &mut self,
            seat: &super::wl_seat::WlSeat, 
            serial: u32, 
        ) -> io::Result<()> {
            //!  move_: start an interactive move
            //! 
            //!  
            //!  Start an interactive, user-driven move of the surface.
            //!  
            //!  This request must be used in response to some sort of user action
            //!  like a button press, key press, or touch down event. The passed
            //!  serial is used to determine the type of interactive move (touch,
            //!  pointer, etc).
            //!  
            //!  The server may ignore move requests depending on the state of
            //!  the surface (e.g. fullscreen or maximized), or if the passed serial
            //!  is no longer valid.
            //!  
            //!  If triggered, the surface will lose the focus of the device
            //!  (wl_pointer, wl_touch, etc) used for the move. It is up to the
            //!  compositor to visually indicate that the move is taking place, such as
            //!  updating a pointer cursor, during the move. There is no guarantee
            //!  that the device focus will return when the move is completed.
            //!  

            const OPCODE: u32 = 5;

            crate::args!(args = 
                (Object, Some(seat.as_ref())),
                (UInt32, serial),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn resize(
            &mut self,
            seat: &super::wl_seat::WlSeat, 
            serial: u32, 
            edges: ResizeEdge,
        ) -> io::Result<()> {
            //!  resize: start an interactive resize
            //! 
            //!  
            //!  Start a user-driven, interactive resize of the surface.
            //!  
            //!  This request must be used in response to some sort of user action
            //!  like a button press, key press, or touch down event. The passed
            //!  serial is used to determine the type of interactive resize (touch,
            //!  pointer, etc).
            //!  
            //!  The server may ignore resize requests depending on the state of
            //!  the surface (e.g. fullscreen or maximized).
            //!  
            //!  If triggered, the client will receive configure events with the
            //!  "resize" state enum value and the expected sizes. See the "resize"
            //!  enum value for more details about what is required. The client
            //!  must also acknowledge configure events using "ack_configure". After
            //!  the resize is completed, the client will receive another "configure"
            //!  event without the resize state.
            //!  
            //!  If triggered, the surface also will lose the focus of the device
            //!  (wl_pointer, wl_touch, etc) used for the resize. It is up to the
            //!  compositor to visually indicate that the resize is taking place,
            //!  such as updating a pointer cursor, during the resize. There is no
            //!  guarantee that the device focus will return when the resize is
            //!  completed.
            //!  
            //!  The edges parameter specifies how the surface should be resized, and
            //!  is one of the values of the resize_edge enum. Values not matching
            //!  a variant of the enum will cause the invalid_resize_edge protocol error.
            //!  The compositor may use this information to update the surface position
            //!  for example when dragging the top left corner. The compositor may also
            //!  use this information to adapt its behavior, e.g. choose an appropriate
            //!  cursor image.
            //!  

            const OPCODE: u32 = 6;

            let edges = edges.into();
            crate::args!(args = 
                (Object, Some(seat.as_ref())),
                (UInt32, serial),
                (UInt32, edges),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_max_size(
            &mut self,
            width: i32,
            height: i32,
        ) -> io::Result<()> {
            //!  set_max_size: set the maximum size
            //! 
            //!  
            //!  Set a maximum size for the window.
            //!  
            //!  The client can specify a maximum size so that the compositor does
            //!  not try to configure the window beyond this size.
            //!  
            //!  The width and height arguments are in window geometry coordinates.
            //!  See xdg_surface.set_window_geometry.
            //!  
            //!  Values set in this way are double-buffered, see wl_surface.commit.
            //!  
            //!  The compositor can use this information to allow or disallow
            //!  different states like maximize or fullscreen and draw accurate
            //!  animations.
            //!  
            //!  Similarly, a tiling window manager may use this information to
            //!  place and resize client windows in a more effective way.
            //!  
            //!  The client should not rely on the compositor to obey the maximum
            //!  size. The compositor may decide to ignore the values set by the
            //!  client and request a larger size.
            //!  
            //!  If never set, or a value of zero in the request, means that the
            //!  client has no expected maximum size in the given dimension.
            //!  As a result, a client wishing to reset the maximum size
            //!  to an unspecified state can use zero for width and height in the
            //!  request.
            //!  
            //!  Requesting a maximum size to be smaller than the minimum size of
            //!  a surface is illegal and will result in an invalid_size error.
            //!  
            //!  The width and height must be greater than or equal to zero. Using
            //!  strictly negative values for width or height will result in a
            //!  invalid_size error.
            //!  

            const OPCODE: u32 = 7;

            crate::args!(args = 
                (Int32, width),
                (Int32, height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_min_size(
            &mut self,
            width: i32,
            height: i32,
        ) -> io::Result<()> {
            //!  set_min_size: set the minimum size
            //! 
            //!  
            //!  Set a minimum size for the window.
            //!  
            //!  The client can specify a minimum size so that the compositor does
            //!  not try to configure the window below this size.
            //!  
            //!  The width and height arguments are in window geometry coordinates.
            //!  See xdg_surface.set_window_geometry.
            //!  
            //!  Values set in this way are double-buffered, see wl_surface.commit.
            //!  
            //!  The compositor can use this information to allow or disallow
            //!  different states like maximize or fullscreen and draw accurate
            //!  animations.
            //!  
            //!  Similarly, a tiling window manager may use this information to
            //!  place and resize client windows in a more effective way.
            //!  
            //!  The client should not rely on the compositor to obey the minimum
            //!  size. The compositor may decide to ignore the values set by the
            //!  client and request a smaller size.
            //!  
            //!  If never set, or a value of zero in the request, means that the
            //!  client has no expected minimum size in the given dimension.
            //!  As a result, a client wishing to reset the minimum size
            //!  to an unspecified state can use zero for width and height in the
            //!  request.
            //!  
            //!  Requesting a minimum size to be larger than the maximum size of
            //!  a surface is illegal and will result in an invalid_size error.
            //!  
            //!  The width and height must be greater than or equal to zero. Using
            //!  strictly negative values for width and height will result in a
            //!  invalid_size error.
            //!  

            const OPCODE: u32 = 8;

            crate::args!(args = 
                (Int32, width),
                (Int32, height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_maximized(
            &mut self,
        ) -> io::Result<()> {
            //!  set_maximized: maximize the window
            //! 
            //!  
            //!  Maximize the surface.
            //!  
            //!  After requesting that the surface should be maximized, the compositor
            //!  will respond by emitting a configure event. Whether this configure
            //!  actually sets the window maximized is subject to compositor policies.
            //!  The client must then update its content, drawing in the configured
            //!  state. The client must also acknowledge the configure when committing
            //!  the new content (see ack_configure).
            //!  
            //!  It is up to the compositor to decide how and where to maximize the
            //!  surface, for example which output and what region of the screen should
            //!  be used.
            //!  
            //!  If the surface was already maximized, the compositor will still emit
            //!  a configure event with the "maximized" state.
            //!  
            //!  If the surface is in a fullscreen state, this request has no direct
            //!  effect. It may alter the state the surface is returned to when
            //!  unmaximized unless overridden by the compositor.
            //!  

            const OPCODE: u32 = 9;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn unset_maximized(
            &mut self,
        ) -> io::Result<()> {
            //!  unset_maximized: unmaximize the window
            //! 
            //!  
            //!  Unmaximize the surface.
            //!  
            //!  After requesting that the surface should be unmaximized, the compositor
            //!  will respond by emitting a configure event. Whether this actually
            //!  un-maximizes the window is subject to compositor policies.
            //!  If available and applicable, the compositor will include the window
            //!  geometry dimensions the window had prior to being maximized in the
            //!  configure event. The client must then update its content, drawing it in
            //!  the configured state. The client must also acknowledge the configure
            //!  when committing the new content (see ack_configure).
            //!  
            //!  It is up to the compositor to position the surface after it was
            //!  unmaximized; usually the position the surface had before maximizing, if
            //!  applicable.
            //!  
            //!  If the surface was already not maximized, the compositor will still
            //!  emit a configure event without the "maximized" state.
            //!  
            //!  If the surface is in a fullscreen state, this request has no direct
            //!  effect. It may alter the state the surface is returned to when
            //!  unmaximized unless overridden by the compositor.
            //!  

            const OPCODE: u32 = 10;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_fullscreen(
            &mut self,
            output: Option<&super::wl_output::WlOutput>, 
        ) -> io::Result<()> {
            //!  set_fullscreen: set the window as fullscreen on an output
            //! 
            //!  
            //!  Make the surface fullscreen.
            //!  
            //!  After requesting that the surface should be fullscreened, the
            //!  compositor will respond by emitting a configure event. Whether the
            //!  client is actually put into a fullscreen state is subject to compositor
            //!  policies. The client must also acknowledge the configure when
            //!  committing the new content (see ack_configure).
            //!  
            //!  The output passed by the request indicates the client's preference as
            //!  to which display it should be set fullscreen on. If this value is NULL,
            //!  it's up to the compositor to choose which display will be used to map
            //!  this surface.
            //!  
            //!  If the surface doesn't cover the whole output, the compositor will
            //!  position the surface in the center of the output and compensate with
            //!  with border fill covering the rest of the output. The content of the
            //!  border fill is undefined, but should be assumed to be in some way that
            //!  attempts to blend into the surrounding area (e.g. solid black).
            //!  
            //!  If the fullscreened surface is not opaque, the compositor must make
            //!  sure that other screen content not part of the same surface tree (made
            //!  up of subsurfaces, popups or similarly coupled surfaces) are not
            //!  visible below the fullscreened surface.
            //!  

            const OPCODE: u32 = 11;

            crate::args!(args = 
                (NObject, output.map(|x| x.as_proxy())),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn unset_fullscreen(
            &mut self,
        ) -> io::Result<()> {
            //!  unset_fullscreen: unset the window as fullscreen
            //! 
            //!  
            //!  Make the surface no longer fullscreen.
            //!  
            //!  After requesting that the surface should be unfullscreened, the
            //!  compositor will respond by emitting a configure event.
            //!  Whether this actually removes the fullscreen state of the client is
            //!  subject to compositor policies.
            //!  
            //!  Making a surface unfullscreen sets states for the surface based on the following:
            //!  * the state(s) it may have had before becoming fullscreen
            //!  * any state(s) decided by the compositor
            //!  * any state(s) requested by the client while the surface was fullscreen
            //!  
            //!  The compositor may include the previous window geometry dimensions in
            //!  the configure event, if applicable.
            //!  
            //!  The client must also acknowledge the configure when committing the new
            //!  content (see ack_configure).
            //!  

            const OPCODE: u32 = 12;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_minimized(
            &mut self,
        ) -> io::Result<()> {
            //!  set_minimized: set the window as minimized
            //! 
            //!  
            //!  Request that the compositor minimize your surface. There is no
            //!  way to know if the surface is currently minimized, nor is there
            //!  any way to unset minimization on this surface.
            //!  
            //!  If you are looking to throttle redrawing when minimized, please
            //!  instead use the wl_surface.frame event for this, as this will
            //!  also work with live previews on windows in Alt-Tab, Expose or
            //!  similar compositor features.
            //!  

            const OPCODE: u32 = 13;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let width = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let height = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let states = match iter.next() {
                            Some(crate::TypedArgument::Array(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Configure {
                            width,
                            height,
                            states: states.as_slice(),
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        assert!(iter.next().is_none());
                        let event = Event::Close {
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        let width = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let height = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::ConfigureBounds {
                            width,
                            height,
                        };
                        listener(&proxy, event);
                    },
                    3 => {
                        let capabilities = match iter.next() {
                            Some(crate::TypedArgument::Array(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::WmCapabilities {
                            capabilities: capabilities.as_slice(),
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 7;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  configure: suggest a surface change
        /// 
        ///  
        ///  This configure event asks the client to resize its toplevel surface or
        ///  to change its state. The configured state should not be applied
        ///  immediately. See xdg_surface.configure for details.
        ///  
        ///  The width and height arguments specify a hint to the window
        ///  about how its surface should be resized in window geometry
        ///  coordinates. See set_window_geometry.
        ///  
        ///  If the width or height arguments are zero, it means the client
        ///  should decide its own window dimension. This may happen when the
        ///  compositor needs to configure the state of the surface but doesn't
        ///  have any information about any previous or expected dimension.
        ///  
        ///  The states listed in the event specify how the width/height
        ///  arguments should be interpreted, and possibly how it should be
        ///  drawn.
        ///  
        ///  Clients must send an ack_configure in response to this event. See
        ///  xdg_surface.configure and xdg_surface.ack_configure for details.
        ///  
        Configure {
            /// width
            width: i32,
            /// height
            height: i32,
            /// states
            states: &'a [u8],
        },
        ///  close: surface wants to be closed
        /// 
        ///  
        ///  The close event is sent by the compositor when the user
        ///  wants the surface to be closed. This should be equivalent to
        ///  the user clicking the close button in client-side decorations,
        ///  if your application has any.
        ///  
        ///  This is only a request that the user intends to close the
        ///  window. The client may choose to ignore this request, or show
        ///  a dialog to ask the user to save their data, etc.
        ///  
        Close {
        },
        ///  configure_bounds: recommended window geometry bounds
        /// 
        ///  
        ///  The configure_bounds event may be sent prior to a xdg_toplevel.configure
        ///  event to communicate the bounds a window geometry size is recommended
        ///  to constrain to.
        ///  
        ///  The passed width and height are in surface coordinate space. If width
        ///  and height are 0, it means bounds is unknown and equivalent to as if no
        ///  configure_bounds event was ever sent for this surface.
        ///  
        ///  The bounds can for example correspond to the size of a monitor excluding
        ///  any panels or other shell components, so that a surface isn't created in
        ///  a way that it cannot fit.
        ///  
        ///  The bounds may change at any point, and in such a case, a new
        ///  xdg_toplevel.configure_bounds will be sent, followed by
        ///  xdg_toplevel.configure and xdg_surface.configure.
        ///  
        ConfigureBounds {
            /// width
            width: i32,
            /// height
            height: i32,
        },
        ///  wm_capabilities: compositor capabilities
        /// 
        ///  
        ///  This event advertises the capabilities supported by the compositor. If
        ///  a capability isn't supported, clients should hide or disable the UI
        ///  elements that expose this functionality. For instance, if the
        ///  compositor doesn't advertise support for minimized toplevels, a button
        ///  triggering the set_minimized request should not be displayed.
        ///  
        ///  The compositor will ignore requests it doesn't support. For instance,
        ///  a compositor which doesn't advertise support for minimized will ignore
        ///  set_minimized requests.
        ///  
        ///  Compositors must send this event once before the first
        ///  xdg_surface.configure event. When the capabilities change, compositors
        ///  must send this event again and then send an xdg_surface.configure
        ///  event.
        ///  
        ///  The configured state should not be applied immediately. See
        ///  xdg_surface.configure for details.
        ///  
        ///  The capabilities are sent as an array of 32-bit unsigned integers in
        ///  native endianness.
        ///  
        WmCapabilities {
            /// capabilities - array of 32-bit capabilities
            capabilities: &'a [u8],
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("xdg_toplevel"),
        7,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_parent"),
            sig!("?o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::xdg_toplevel::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_title"),
            sig!("s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_app_id"),
            sig!("s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("show_window_menu"),
            sig!("ouii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_seat::INTERFACE), None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("move"),
            sig!("ou"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_seat::INTERFACE), None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("resize"),
            sig!("ouu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_seat::INTERFACE), None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_max_size"),
            sig!("ii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_min_size"),
            sig!("ii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_maximized"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("unset_maximized"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_fullscreen"),
            sig!("?o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_output::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("unset_fullscreen"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_minimized"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("configure"),
            sig!("iia"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("close"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("configure_bounds"),
            sig!("ii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("wm_capabilities"),
            sig!("a"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// invalid_resize_edge - provided value is         not a valid variant of the resize_edge enum
        pub const INVALID_RESIZE_EDGE: Error = Error(0);

        /// invalid_parent - invalid parent toplevel
        pub const INVALID_PARENT: Error = Error(1);

        /// invalid_size - client provided an invalid min or max size
        pub const INVALID_SIZE: Error = Error(2);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

    ///  resize_edge: edge values for resizing
    /// 
    ///  
    ///  These values are used to indicate which edge of a surface
    ///  is being dragged in a resize operation.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct ResizeEdge(u32);

    impl ResizeEdge {
        /// none - 
        pub const NONE: ResizeEdge = ResizeEdge(0);

        /// top - 
        pub const TOP: ResizeEdge = ResizeEdge(1);

        /// bottom - 
        pub const BOTTOM: ResizeEdge = ResizeEdge(2);

        /// left - 
        pub const LEFT: ResizeEdge = ResizeEdge(4);

        /// top_left - 
        pub const TOP_LEFT: ResizeEdge = ResizeEdge(5);

        /// bottom_left - 
        pub const BOTTOM_LEFT: ResizeEdge = ResizeEdge(6);

        /// right - 
        pub const RIGHT: ResizeEdge = ResizeEdge(8);

        /// top_right - 
        pub const TOP_RIGHT: ResizeEdge = ResizeEdge(9);

        /// bottom_right - 
        pub const BOTTOM_RIGHT: ResizeEdge = ResizeEdge(10);

    }

    impl From<u32> for ResizeEdge {
        fn from(value: u32) -> Self {
            ResizeEdge(value)
        }
    }

    impl From<i32> for ResizeEdge {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            ResizeEdge::from(value as u32)
        }
    }

    impl From<ResizeEdge> for u32 {
        fn from(value: ResizeEdge) -> Self {
            value.0
        }
    }

    impl From<ResizeEdge> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: ResizeEdge) -> Self {
            value.0 as i32
        }
    }

    ///  state: types of state on the surface
    /// 
    ///  
    ///  The different state values used on the surface. This is designed for
    ///  state values like maximized, fullscreen. It is paired with the
    ///  configure event to ensure that both the client and the compositor
    ///  setting the state can be synchronized.
    ///  
    ///  States set in this way are double-buffered, see wl_surface.commit.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct State(u32);

    impl State {
        /// maximized - the surface is maximized
        pub const MAXIMIZED: State = State(1);

        /// fullscreen - the surface is fullscreen
        pub const FULLSCREEN: State = State(2);

        /// resizing - the surface is being resized
        pub const RESIZING: State = State(3);

        /// activated - the surface is now activated
        pub const ACTIVATED: State = State(4);

        /// tiled_left - 
        pub const TILED_LEFT: State = State(5);

        /// tiled_right - 
        pub const TILED_RIGHT: State = State(6);

        /// tiled_top - 
        pub const TILED_TOP: State = State(7);

        /// tiled_bottom - 
        pub const TILED_BOTTOM: State = State(8);

        /// suspended - 
        pub const SUSPENDED: State = State(9);

        /// constrained_left - 
        pub const CONSTRAINED_LEFT: State = State(10);

        /// constrained_right - 
        pub const CONSTRAINED_RIGHT: State = State(11);

        /// constrained_top - 
        pub const CONSTRAINED_TOP: State = State(12);

        /// constrained_bottom - 
        pub const CONSTRAINED_BOTTOM: State = State(13);

    }

    impl From<u32> for State {
        fn from(value: u32) -> Self {
            State(value)
        }
    }

    impl From<i32> for State {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            State::from(value as u32)
        }
    }

    impl From<State> for u32 {
        fn from(value: State) -> Self {
            value.0
        }
    }

    impl From<State> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: State) -> Self {
            value.0 as i32
        }
    }

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct WmCapabilities(u32);

    impl WmCapabilities {
        /// window_menu - show_window_menu is available
        pub const WINDOW_MENU: WmCapabilities = WmCapabilities(1);

        /// maximize - set_maximized and unset_maximized are available
        pub const MAXIMIZE: WmCapabilities = WmCapabilities(2);

        /// fullscreen - set_fullscreen and unset_fullscreen are available
        pub const FULLSCREEN: WmCapabilities = WmCapabilities(3);

        /// minimize - set_minimized is available
        pub const MINIMIZE: WmCapabilities = WmCapabilities(4);

    }

    impl From<u32> for WmCapabilities {
        fn from(value: u32) -> Self {
            WmCapabilities(value)
        }
    }

    impl From<i32> for WmCapabilities {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            WmCapabilities::from(value as u32)
        }
    }

    impl From<WmCapabilities> for u32 {
        fn from(value: WmCapabilities) -> Self {
            value.0
        }
    }

    impl From<WmCapabilities> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: WmCapabilities) -> Self {
            value.0 as i32
        }
    }

}

pub use self::xdg_toplevel::XdgToplevel;

pub mod xdg_popup {
    //!  xdg_popup: short-lived, popup surfaces for menus
    //! 
    //!  
    //!  A popup surface is a short-lived, temporary surface. It can be used to
    //!  implement for example menus, popovers, tooltips and other similar user
    //!  interface concepts.
    //!  
    //!  A popup can be made to take an explicit grab. See xdg_popup.grab for
    //!  details.
    //!  
    //!  When the popup is dismissed, a popup_done event will be sent out, and at
    //!  the same time the surface will be unmapped. See the xdg_popup.popup_done
    //!  event for details.
    //!  
    //!  Explicitly destroying the xdg_popup object will also dismiss the popup and
    //!  unmap the surface. Clients that want to dismiss the popup when another
    //!  surface of their own is clicked should dismiss the popup using the destroy
    //!  request.
    //!  
    //!  A newly created xdg_popup will be stacked on top of all previously created
    //!  xdg_popup surfaces associated with the same xdg_toplevel.
    //!  
    //!  The parent of an xdg_popup must be mapped (see the xdg_surface
    //!  description) before the xdg_popup itself.
    //!  
    //!  The client must call wl_surface.commit on the corresponding wl_surface
    //!  for the xdg_popup state to take effect.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `xdg_popup` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct XdgPopup(Proxy);

    impl fmt::Debug for XdgPopup {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("XdgPopup").field(&self.0).finish()
        }
    }

    impl From<Proxy> for XdgPopup {
        fn from(proxy: Proxy) -> Self {
            XdgPopup(proxy)
        }
    }

    impl From<XdgPopup> for Proxy {
        fn from(proxy: XdgPopup) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for XdgPopup {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for XdgPopup {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl XdgPopup {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl XdgPopup {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: remove xdg_popup interface
            //! 
            //!  
            //!  This destroys the popup. Explicitly destroying the xdg_popup
            //!  object will also dismiss the popup, and unmap the surface.
            //!  
            //!  If this xdg_popup is not the "topmost" popup, the
            //!  xdg_wm_base.not_the_topmost_popup protocol error will be sent.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn grab(
            &mut self,
            seat: &super::wl_seat::WlSeat, 
            serial: u32, 
        ) -> io::Result<()> {
            //!  grab: make the popup take an explicit grab
            //! 
            //!  
            //!  This request makes the created popup take an explicit grab. An explicit
            //!  grab will be dismissed when the user dismisses the popup, or when the
            //!  client destroys the xdg_popup. This can be done by the user clicking
            //!  outside the surface, using the keyboard, or even locking the screen
            //!  through closing the lid or a timeout.
            //!  
            //!  If the compositor denies the grab, the popup will be immediately
            //!  dismissed.
            //!  
            //!  This request must be used in response to some sort of user action like a
            //!  button press, key press, or touch down event. The serial number of the
            //!  event should be passed as 'serial'.
            //!  
            //!  The parent of a grabbing popup must either be an xdg_toplevel surface or
            //!  another xdg_popup with an explicit grab. If the parent is another
            //!  xdg_popup it means that the popups are nested, with this popup now being
            //!  the topmost popup.
            //!  
            //!  Nested popups must be destroyed in the reverse order they were created
            //!  in, e.g. the only popup you are allowed to destroy at all times is the
            //!  topmost one.
            //!  
            //!  When compositors choose to dismiss a popup, they may dismiss every
            //!  nested grabbing popup as well. When a compositor dismisses popups, it
            //!  will follow the same dismissing order as required from the client.
            //!  
            //!  If the topmost grabbing popup is destroyed, the grab will be returned to
            //!  the parent of the popup, if that parent previously had an explicit grab.
            //!  
            //!  If the parent is a grabbing popup which has already been dismissed, this
            //!  popup will be immediately dismissed. If the parent is a popup that did
            //!  not take an explicit grab, an error will be raised.
            //!  
            //!  During a popup grab, the client owning the grab will receive pointer
            //!  and touch events for all their surfaces as normal (similar to an
            //!  "owner-events" grab in X11 parlance), while the top most grabbing popup
            //!  will always have keyboard focus.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (Object, Some(seat.as_ref())),
                (UInt32, serial),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn reposition(
            &mut self,
            positioner: &super::xdg_positioner::XdgPositioner, 
            token: u32, 
        ) -> io::Result<()> {
            //!  reposition: recalculate the popup's location
            //! 
            //!  
            //!  Reposition an already-mapped popup. The popup will be placed given the
            //!  details in the passed xdg_positioner object, and a
            //!  xdg_popup.repositioned followed by xdg_popup.configure and
            //!  xdg_surface.configure will be emitted in response. Any parameters set
            //!  by the previous positioner will be discarded.
            //!  
            //!  The passed token will be sent in the corresponding
            //!  xdg_popup.repositioned event. The new popup position will not take
            //!  effect until the corresponding configure event is acknowledged by the
            //!  client. See xdg_popup.repositioned for details. The token itself is
            //!  opaque, and has no other special meaning.
            //!  
            //!  If multiple reposition requests are sent, the compositor may skip all
            //!  but the last one.
            //!  
            //!  If the popup is repositioned in response to a configure event for its
            //!  parent, the client should send an xdg_positioner.set_parent_configure
            //!  and possibly an xdg_positioner.set_parent_size request to allow the
            //!  compositor to properly constrain the popup.
            //!  
            //!  If the popup is repositioned together with a parent that is being
            //!  resized, but not in response to a configure event, the client should
            //!  send an xdg_positioner.set_parent_size request.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (Object, Some(positioner.as_ref())),
                (UInt32, token),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let x = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let y = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let width = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let height = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Configure {
                            x,
                            y,
                            width,
                            height,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        assert!(iter.next().is_none());
                        let event = Event::PopupDone {
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        let token = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Repositioned {
                            token,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 7;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  configure: configure the popup surface
        /// 
        ///  
        ///  This event asks the popup surface to configure itself given the
        ///  configuration. The configured state should not be applied immediately.
        ///  See xdg_surface.configure for details.
        ///  
        ///  The x and y arguments represent the position the popup was placed at
        ///  given the xdg_positioner rule, relative to the upper left corner of the
        ///  window geometry of the parent surface.
        ///  
        ///  For version 2 or older, the configure event for an xdg_popup is only
        ///  ever sent once for the initial configuration. Starting with version 3,
        ///  it may be sent again if the popup is setup with an xdg_positioner with
        ///  set_reactive requested, or in response to xdg_popup.reposition requests.
        ///  
        Configure {
            /// x - x position relative to parent surface window geometry
            x: i32,
            /// y - y position relative to parent surface window geometry
            y: i32,
            /// width - window geometry width
            width: i32,
            /// height - window geometry height
            height: i32,
        },
        ///  popup_done: popup interaction is done
        /// 
        ///  
        ///  The popup_done event is sent out when a popup is dismissed by the
        ///  compositor. The client should destroy the xdg_popup object at this
        ///  point.
        ///  
        PopupDone {
        },
        ///  repositioned: signal the completion of a repositioned request
        /// 
        ///  
        ///  The repositioned event is sent as part of a popup configuration
        ///  sequence, together with xdg_popup.configure and lastly
        ///  xdg_surface.configure to notify the completion of a reposition request.
        ///  
        ///  The repositioned event is to notify about the completion of a
        ///  xdg_popup.reposition request. The token argument is the token passed
        ///  in the xdg_popup.reposition request.
        ///  
        ///  Immediately after this event is emitted, xdg_popup.configure and
        ///  xdg_surface.configure will be sent with the updated size and position,
        ///  as well as a new configure serial.
        ///  
        ///  The client should optionally update the content of the popup, but must
        ///  acknowledge the new popup configuration for the new position to take
        ///  effect. See xdg_surface.ack_configure for details.
        ///  
        Repositioned {
            /// token - reposition request token
            token: u32,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("xdg_popup"),
        7,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("grab"),
            sig!("ou"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_seat::INTERFACE), None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("reposition"),
            sig!("ou"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::xdg_positioner::INTERFACE), None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("configure"),
            sig!("iiii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("popup_done"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("repositioned"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// invalid_grab - tried to grab after being mapped
        pub const INVALID_GRAB: Error = Error(0);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::xdg_popup::XdgPopup;

