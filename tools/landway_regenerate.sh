#!/bin/sh

# Regenerate all landway code.

set -eu
cd "$(dirname "$0")"/..

rx() {
  echo "+ $*"
  "$@"
}

# Generate a module.
gen() {
  modname="$(cargo run --quiet --package landway-gen -- "$@")"

  # Add to the module file.
  if [ -n "${needs_feature:-}" ]; then
    echo "    #[cfg(feature = \"$modname\")]">> "$modfile"
  fi
  echo "    pub use super::$modname::*;" >> "$modfile"
  if [ -n "${needs_feature:-}" ]; then
    echo "#[cfg(feature = \"$modname\")]">> "$modlower"
  fi
  echo "pub mod $modname;" >> "$modlower"
}

# Create the protocol dir.
protdir="./crates/landway/src/protocol"
mkdir -p "$protdir"

# Set up the module file.
modfile="$protdir/mod.rs"
modlower="$(mktemp)"
cat >"$modfile" <<EOF
// MIT/Apache2 License
// This file is automatically generated! Do not edit this file manually.
// Please see the landway-regenerate.sh code for more info.

//! Automatically generated Wayland protocol implementations.

#[allow(unused_imports)]
mod __all {
EOF

# Generate all modules.
srcdir="./external/wayland-protocols"
gen "./external/wayland/protocol/wayland.xml" "$protdir"
needs_feature=1
gen "$srcdir/stable/xdg-shell/xdg-shell.xml" "$protdir"

echo "}" >> "$modfile"
echo >> "$modfile"
cat "$modlower" >> "$modfile"
rm "$modlower"
